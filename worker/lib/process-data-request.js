const fs = require('fs').promises;
const syncFs = require('fs');
const path = require('path');
const AdmZip = require('adm-zip');
const cfsign = require('aws-cloudfront-sign');
const { getSigningParams } = require('../../lib/cloudfront');
const { s3, AWS_S3_BUCKET } = require('../../lib/s3');
const { sendEmailTemplateSES, translateEmail } = require('../../lib/email');
const DataRequestHistory = require('../../models/data-request-history');
const User = require('../../models/user');
const UserMetadata = require('../../models/user-metadata');
const Chat = require('../../models/chat');
const Question = require('../../models/question');
const Comment = require('../../models/comment');
const Story = require('../../models/story');
const PurchaseReceipt = require('../../models/purchase-receipt');
const CoinPurchaseReceipt = require('../../models/coin-purchase-receipt');
const SuperLikePurchaseReceipt = require('../../models/super-like-purchase-receipt');
const NeuronPurchaseReceipt = require('../../models/neuron-purchase-receipt');
const BoostPurchaseReceipt = require('../../models/boost-purchase-receipt');
const StripeReceipt = require('../../models/stripe-receipt');
const AdReceipt = require('../../models/ad-receipt');
const HideList = require('../../models/hide-list');
const PersonalityQuizResult = require('../../models/personality-quiz-result');
const Notification = require('../../models/notification');
const CoinTransaction = require('../../models/coin-transaction');
const DeleteAccountAttempt = require('../../models/delete-account-attempt');
const SavedQuestion = require('../../models/saved-question');
const OpenaiTransaction = require('../../models/openai-transaction');
const QuestionViewData = require('../../models/question-view-data');
const Action = require('../../models/action');
const Block = require('../../models/block');
const Follow = require('../../models/follow');
const HideOnSocial = require('../../models/hide-on-social');
const ProfileView = require('../../models/profile-view');
const ProfileVote = require('../../models/profile-vote');
const Award = require('../../models/award');
const BoostMetric = require('../../models/boost-metric');
const Interest = require('../../models/interest');
const LivenessChallenge = require('../../models/liveness-challenge');
const QuestionCandidate = require('../../models/question-candidate');
const Referral = require('../../models/referral');
const Report = require('../../models/report');
const PostReport = require('../../models/post-report');
const Translation = require('../../models/translation');
const { sendAutomatedReply } = require('../../lib/chat');
const { stringify } = require('flatted');
const { getStripeDataExport } = require('../../lib/stripe-data-export');

const log = (msg) => console.log(`[data-export]: ${msg}`);

const config = {
  retryLimit: 3,
  bucketName: process.env.AWS_EXPIRY_S3_BUCKET || 'MOCK_S3_EXPIRY_BUCKET',
};

let dataExportRunning = false;

const downloadFile = async (directory, key, mediaId, userId) => {
  const fileName = `${mediaId}${path.extname(key)}`;
  const filePath = path.join(directory, fileName);

  try {
    const s3Stream = s3.getObject({ Bucket: AWS_S3_BUCKET, Key: key }).createReadStream();
    const fileStream = syncFs.createWriteStream(filePath);

    await new Promise((resolve, reject) => {
      s3Stream
        .on('error', reject)
        .pipe(fileStream)
        .on('error', reject)
        .on('finish', resolve);
    });

    return fileName;
  } catch (err) {
    log(`userId: ${userId} - Error downloading file ${key} during data export: ${err.message}`);
    try { await fs.unlink(filePath); } catch (_) {} // Cleanup partial file
    return 'Not found';
  }
};

const uploadToS3 = async (zipFilePath) => {
  if (process.env.TESTING) {
    return { key: `${Date.now()}.zip`, url: 'MOCK_URL' };
  }

  return new Promise((resolve, reject) => {
    const readStream = syncFs.createReadStream(zipFilePath);
    const params = {
      Bucket: config.bucketName,
      Key: `data-export/${Date.now()}.zip`,
      Body: readStream,
    };

    s3.upload(params, (err, data) => {
      if (err) return reject(err);
      resolve({ key: data.Key, url: data.Location });
    });

    readStream.on('error', (err) => {
      reject(err);
    });
  });
};

const sendEmail = async (user, signedUrl, userId) => {
  const template = 'data-download-v1';
  const emailContent = [
    {
      name: 'HI_NAME',
      content: translateEmail({ phrase: 'Hello {{name}}', locale: user.locale }, { name: user.firstName }),
    },
    {
      name: 'MESSAGE',
      content: translateEmail({
        phrase: `We have compiled all the information associated with your Boo account. You can download it using the link below, valid for 7 days. If you have any questions, please reach out to us.`,
        locale: user.locale,
      }),
    },
    {
      name: 'DOWNLOAD_NOW',
      content: translateEmail({ phrase: 'Download Data', locale: user.locale }),
    },
    {
      name: 'DOWNLOAD_LINK',
      content: signedUrl,
    },
    {
      name: 'THANK_YOU',
      content: translateEmail({ phrase: 'Love,', locale: user.locale }),
    },
    {
      name: 'TEAM_BOO',
      content: translateEmail({ phrase: 'The Boo Team', locale: user.locale }),
    },
  ];

  const userData = await User.findById(userId);
  if (userData.versionAtLeast('1.13.64') && !userData.banNotice) {
    const dataDownloadLink = userData.versionAtLeast('1.13.73') ? `[Download Data](${signedUrl})` : signedUrl;
    const messageText = `${translateEmail({ phrase: `We have compiled all the information associated with your Boo account. You can download it using the link below, valid for 7 days.`, locale: user.locale })}\n\n${dataDownloadLink}`;
    await sendAutomatedReply(userData, messageText);
  } else {
    await sendEmailTemplateSES(userData, template, 'Your Boo Data is Now Available', emailContent);
  }
};

const getProfileAndAccount = async (USER_ID) => {
  let user = await User.findById(USER_ID)
    .select({
      _id: 0,
      firstName: 1,
      handle: 1,
      location: 1,
      country: 1,
      state: 1,
      city: 1,
      signupCountry: 1,
      googlePlayCountry: 1,
      'personality.mbti': 1,
      enneagram: 1,
      gender: 1,
      birthday: 1,
      age: 1,
      height: 1,
      ethnicities: 1,
      sexuality: 1,
      sexualityVisibility: 1,
      relationshipType: 1,
      datingSubPreferences: 1,
      relationshipStatus: 1,
      horoscope: 1,
      education: 1,
      work: 1,
      description: 1,
      moreAboutUser: 1,
      customPersonalityCompatibility: 1,
      audioDescription: 1,
      audioDescriptionWaveform: 1,
      audioDescriptionDuration: 1,
      prompts: 1,
      email: 1,
      phoneNumber: 1,
      interestNames: 1,
      interestPoints: 1,
      karma: 1,
      karmaTier: 1,
      languages: 1,
      awards: 1,
      timezone: 1,
      ipData: 1,
      preferences: 1,
      preferencesModifiedAt: 1,
      profileModifiedAt: 1,
      recommendationsExhaustedAt: 1,
      incomingRequestsPreferences: 1,
      fcmToken: 1,
      fcmTokenUpdatedAt: 1,
      pictures: 1,
      createdAt: 1,
      searchable: 1,
      hidden: 1,
      hideQuestions: 1,
      hideComments: 1,
      hideHoroscope: 1,
      hideLocation: 1,
      hideProfileViews: 1,
      hideCity: 1,
      hideReadReceipts: 1,
      premiumExpiration: 1,
      productIdPurchased: 1,
      boostExpiration: 1,
      boostDurationMinutes: 1,
      appVersion: 1,
      darkMode: 1,
      vibrationsDisabled: 1,
      messagesTheme: 1,
      changeHomeScreenToSocial: 1,
      dataSaver: 1,
      hideMyFollowerCount: 1,
      hideMyAwards: 1,
      hideMyKarma: 1,
      hideMyAge: 1,
      socialPreferences: 1,
      socialPreferencesActivated: 1,
      customFeeds: 1,
      pushNotificationSettings: 1,
      wingmanSettings: 1,
      aiSettings: 1,
      security: 1,
      termsAcceptedDate: 1,
      termsAcceptedHistory: 1,
      'verification.status': 1,
      'verification.updatedAt': 1,
      'verification.pictures': 1,
      'verification.rejectionReason': 1,
      hiddenContacts: 1,
      hideFromKeywords: 1,
      hideFromNearby: 1,
      hiddenInterests: 1,
      advertisingId: 1,
      optOutOfAdTargeting: 1,
      autoplay: 1,
      stripeCustomerId: 1,
      os: 1,
      osVersion: 1,
      phoneModel: 1,
      deviceId: 1,
      deviceIdHistory: 1,
      webDeviceId: 1,
      appDeviceId: 1,
      isPhysicalDevice: 1,
      deviceLanguage: 1,
      locale: 1,
      countryLocale: 1,
      socialFeedLanguage: 1,
      signupSource: 1,
      webSignupCategory: 1,
      webSignupPage: 1,
      webFirstVisitCategory: 1,
      webFirstVisitPage: 1,
      webFirstVisitReferringDomain: 1,
      webFirstVisitReferringURL: 1,
      approveAllFollowers: 1,
      autoFollowLikes: 1,
      autoFollowMatches: 1,
      numSuperLikes: 1,
      numBooAINeurons: 1,
      howDidYouHearAboutUs: 1,
      'metrics.numReferralsMade': 1,
      'metrics.numLikesSent': 1,
      'metrics.numPassesSent': 1,
      'metrics.numApprovalsSent': 1,
      'metrics.numRejectionsSent': 1,
      'metrics.numUnmatchesSent': 1,
      'metrics.numBlocksSent': 1,
      'metrics.numMessagesSent': 1,
      'metrics.numMatches': 1,
      'metrics.numMatchesFirstMessageSent': 1,
      'metrics.numMatchesReplySent': 1,
      'metrics.numMatchesBothMessaged': 1,
      'metrics.numDMSent': 1,
      'metrics.numDMSentFromSwiping': 1,
      'metrics.numDMSentFromSocial': 1,
      'metrics.numDMSentFromStories': 1,
      'metrics.numSuperLikesPurchased': 1,
      'metrics.numSuperLikesSent': 1,
      'metrics.numNeuronsPurchased': 1,
      'metrics.numNeuronsUsed': 1,
      'metrics.numSessions': 1,
      'metrics.numEngagedSessions': 1,
      'metrics.numSecondsEngagementTime': 1,
      'metrics.avgSecondsEngagementTime': 1,
      'metrics.numDeleteAccountAttempts': 1,
      'metrics.numDeleteAccountAttemptsCancelled': 1,
      'metrics.numFeedbackSubmitted': 1,
      'metrics.numPurchases': 1,
      'metrics.numCoinPurchases': 1,
      'metrics.numCoinsPurchased': 1,
      'metrics.numStripePurchases': 1,
      'metrics.numRefunds': 1,
      'metrics.revenueRefunded': 1,
      'metrics.revenue': 1,
      'metrics.coinRevenue': 1,
      'metrics.numSuperLikePurchases': 1,
      'metrics.superLikeRevenue': 1,
      'metrics.numNeuronPurchases': 1,
      'metrics.neuronRevenue': 1,
      'metrics.stripeRevenue': 1,
      'metrics.saleRevenue': 1,
      'metrics.numSalePurchases': 1,
      'metrics.lastSeen': 1,
      'metrics.lastSeenAndroid': 1,
      'metrics.lastSeenIos': 1,
      'metrics.lastSeenWeb': 1,
      'metrics.numFollowers': 1,
      'metrics.numFollowing': 1,
      'metrics.numFollowRequests': 1,
      'metrics.numProfileViews': 1,
      'metrics.numAwardsSent': 1,
      'metrics.numQuestions': 1,
      'metrics.numComments': 1,
      'metrics.numStories': 1,
      'metrics.numPostLikesSent': 1,
      'metrics.numPostUnlikesSent': 1,
      'metrics.numPostShares': 1,
      'metrics.numPostClicks': 1,
      'metrics.numSecondsReadingOnFeed': 1,
      'metrics.numSecondsReadingComments': 1,
      'metrics.retention': 1,
      'metrics.activeDates': 1,
      'metrics.numCoinAdsWatched': 1,
      'metrics.numCoinsEarnedFromAds': 1,
      'metrics.numViewLastSeenPurchased': 1,
      'metrics.purchasedPremiumFrom': 1,
      'metrics.purchasedCoinsFrom': 1,
      'metrics.revivalUsedAt': 1,
      'metrics.numRevivalUsed': 1,
      'metrics.numBoostUsed': 1,
      'metrics.numLiftOffUsed': 1,
      'events.finished_signup': 1,
      'events.enterDeleteAccountFlow': 1,
      'events.enterCoinsPage': 1,
      'events.enterSuperLovePage': 1,
      'events.enterSocialPage': 1,
      'events.referralLinkCreated': 1,
      'events.enterInvitePage': 1,
      'events.sharePersonality': 1,
      'events.tap_dimension_icon': 1,
      'events.change_dimension': 1,
      'events.viewed_faq': 1,
      'events.open_boo_ai': 1,
      'events.viewed_perks': 1,
      'appsflyer.appsflyer_id': 1,
      'appsflyer.payload.af_adset_id': 1,
      'appsflyer.payload.af_ad_type': 1,
      'appsflyer.payload.retargeting_conversion_type': 1,
      'appsflyer.payload.network': 1,
      'appsflyer.payload.is_first_launch': 1,
      'appsflyer.payload.af_click_lookback': 1,
      'appsflyer.payload.click_time': 1,
      'appsflyer.payload.match_type': 1,
      'appsflyer.payload.af_channel': 1,
      'appsflyer.payload.af_viewthrough_lookback': 1,
      'appsflyer.payload.campaign_id': 1,
      'appsflyer.payload.lat': 1,
      'appsflyer.payload.install_time': 1,
      'appsflyer.payload.af_c_id': 1,
      'appsflyer.payload.media_source': 1,
      'appsflyer.payload.ad_event_id': 1,
      'appsflyer.payload.af_siteid': 1,
      'appsflyer.payload.af_status': 1,
      'appsflyer.payload.referrer_gclid': 1,
      'appsflyer.payload.af_ad_id': 1,
      'appsflyer.payload.af_reengagement_window': 1,
      'appsflyer.payload.af_ad': 1,
      'appsflyer.payload.status': 1,
      'appsflyer.payload.af_message': 1,
      'appsflyer.payload.adgroup_id': 1,
      'appsflyer.payload.is_fb': 1,
      'appsflyer.payload.is_paid': 1,
      'appsflyer.payload.ad_id': 1,
      'appsflyer.payload.adset_id': 1,
      'appsflyer.payload.is_mobile_data_terms_signed': 1,
      'appsflyer.payload.af_dp': 1,
      'appsflyer.payload.event': 1,
      'appsflyer.payload.clickid': 1,
      'appsflyer.payload.event_id': 1,
      'appsflyer.payload.af_sub_siteid': 1,
      'appsflyer.payload.is_incentivized': 1,
      'appsflyer.payload.af_ip': 1,
      'appsflyer.payload.advertising_id': 1,
      'appsflyer.payload.af_ref': 1,
      'appsflyer.payload.af_os': 1,
      'appsflyer.payload.is_retargeting': 1,
      'appsflyer.payload.af_model': 1,
      'appsflyer.payload.af_pmod_lookback_window': 1,
      'appsflyer.payload.af_ua': 1,
      'appsflyer.payload.af_campaign_type': 1,
      'appsflyer.payload.af_os_version': 1,
      'appsflyer.payload.af_sub1': 1,
      'appsflyer.payload.af_lang': 1,
      'appsflyer.payload.af_vt_pmod_lookback_window': 1,
      'appsflyer.payload.webFirstVisitReferringURL': 1,
      'appsflyer.payload.shortlink': 1,
      'appsflyer.payload.webFirstVisitPage': 1,
      'appsflyer.payload.webDeviceId': 1,
      'appsflyer.payload.http_referrer': 1,
      'appsflyer.payload.partner': 1,
      'appsflyer.payload.request_id': 1,
      'appsflyer.payload.idfa': 1,
      'appsflyer.payload.af_inactivity_window': 1,
      'appsflyer.payload.deep_link_value': 1,
      'appsflyer.payload.fbclid': 1,
      'appsflyer.payload.sha1_idfa': 1,
      'appsflyer.payload.keyword_id': 1,
      'appsflyer.payload.af_keywords': 1,
      'appsflyer.payload.af_web_dp': 1,
      'appsflyer.payload.ad_ad_id': 1,
    })
    .lean();

  // user metadata
  const user_metadata = await UserMetadata.findOne({ user: USER_ID })
    .select({
      _id: 0,
      coins: 1,
      directMessageRewardReceived: 1,
      biographyRewardReceived: 1,
      pictureRewardReceived: 1,
      emailRewardReceived: 1,
      verifyProfileRewardReceived: 1,
      postInTwoDimensionsRewardReceived: 1,
      shareToSocialMediaRewardReceived: 1,
      rateAppRewardReceived: 1,
      detailedReviewRewardReceived: 1,
      enablePushNotificationsRewardReceived: 1,
      enableLocationRewardReceived: 1,
      enableTrackingRewardReceived: 1,
      howDidYouHearAboutUsRewardReceived: 1,
      cancelDeleteAccountRewardReceived: 1,
      firstDimensionPosted: 1,
      numLoginRewardsReceived: 1,
      loginRewardReceivedDate: 1,
      qodRewardsReceived: 1,
      translationRewardsReceived: 1,
      enneagramRewardReceived: 1,
      audioDescriptionRewardReceived: 1,
      quizAnswerRewardReceived: 1,
      profileCommentRewardReceived: 1,
      picturesRewardTier: 1,
      promptsRewardTier: 1,
      interestsRewardTier: 1,
      sendDMRewardTier: 1,
      postQuestionsRewardTier: 1,
      postStoriesRewardTier: 1,
      sharePostsRewardTier: 1,
      day0LoginRewardReceived: 1,
      day1LoginRewardReceived: 1,
      day2LoginRewardReceived: 1,
      day3LoginRewardReceived: 1,
      day4LoginRewardReceived: 1,
      day5LoginRewardReceived: 1,
      day6LoginRewardReceived: 1,
      directMessagesSent: 1,
      coinsSpentOnDirectMessages: 1,
    })
    .lean();

  user = {
    ...user,
    ...user_metadata,
  };

  return user;
};

const getChatsAndMessages = async (USER_ID) =>
  await Chat.aggregate([
    {
      $match: {
        users: USER_ID,
        deletedAt: null,
        bannedUsers: { $in: [null, [], USER_ID] },
      },
    },
    {
      $lookup: {
        from: 'messages',
        let: { chatId: '$_id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [{ $eq: ['$chat', '$$chatId'] }, { $eq: ['$sender', USER_ID] }],
              },
            },
          },
          {
            $sort: {
              createdAt: -1,
            },
          },
          {
            $project: {
              _id: 0,
              createdAt: 1,
              text: 1,
              image: 1,
              aspectRatio: 1,
              gif: 1,
              audio: 1,
              audioWaveform: 1,
              audioDuration: 1,
              video: 1,
              unsent: 1,
            },
          },
        ],
        as: 'messagesSentByMe',
      },
    },
    {
      $project: {
        _id: 0,
        createdAt: 1,
        dndPostFrom: 1,
        dndMessageFrom: 1,
        lastMessageTime: 1,
        numMessages: 1,
        pending: 1,
        messaged: 1,
        replied: 1,
        numMinutesUntilFirstReply: 1,
        initiatedByDM: 1,
        initiatedBySuperLike: 1,
        groupChat: 1,
        groupChatName: 1,
        messagesSentByMe: 1,
        isMuted: { $in: [USER_ID, { $ifNull: ['$muted', []] }] },
        isPinned: { $in: [USER_ID, { $ifNull: ['$pinned', []] }] },
        isHidden: { $in: [USER_ID, { $ifNull: ['$hidden', []] }] },
        isFirstMessageByMe: { $eq: [USER_ID, '$firstMessageBy'] },
        isPendingOnMe: { $eq: [USER_ID, '$pendingUser'] },
        numUnreadMessages: { $ifNull: [`$readReceipts.${USER_ID}.numUnreadMessages`, 0] },
        numSentMessages: { $ifNull: [`$readReceipts.${USER_ID}.numSentMessages`, 0] },
      },
    },
  ]);

const getPosts = async (USER_ID) =>
  await Question.find({ createdBy: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      title: 1,
      text: 1,
      image: 1,
      altText: 1,
      aspectRatio: 1,
      audio: 1,
      audioWaveform: 1,
      audioDuration: 1,
      gif: 1,
      'poll.options': 1,
      interestName: 1,
      hashtags: 1,
      numComments: 1,
      numLikes: 1,
      language: 1,
      awards: 1,
    })
    .lean();

const getComments = async (USER_ID) =>
  await Comment.find({ createdBy: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      text: 1,
      vote: 1,
      image: 1,
      aspectRatio: 1,
      audio: 1,
      audioWaveform: 1,
      audioDuration: 1,
      gif: 1,
      numComments: 1,
      numLikes: 1,
      language: 1,
      awards: 1,
    })
    .lean();

const getStories = async (USER_ID) =>
  await Story.find({ createdBy: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      image: 1,
      textData: 1,
      visibility: 1,
      backgroundColor: 1,
      numUsersWhoViewed: 1,
      numUsersWhoLiked: 1,
    })
    .lean();

const getInfinityPurchases = async (USER_ID) =>
  await PurchaseReceipt.find({ user: USER_ID })
    .select({
      _id: 0,
      purchaseDate: 1,
      expirationDate: 1,
      renewalNumber: 1,
      productId: 1,
      currency: 1,
      price: 1,
      purchasedFrom: 1,
    })
    .lean();

const getCoinPurchases = async (USER_ID) =>
  await CoinPurchaseReceipt.find({ user: USER_ID })
    .select({
      _id: 0,
      purchaseDate: 1,
      productId: 1,
      currency: 1,
      price: 1,
      purchasedFrom: 1,
    })
    .lean();

const getSuperLikePurchases = async (USER_ID) =>
  await SuperLikePurchaseReceipt.find({ user: USER_ID })
    .select({
      _id: 0,
      purchaseDate: 1,
      productId: 1,
      currency: 1,
      price: 1,
    })
    .lean();

const getNeuronPurchases = async (USER_ID) =>
  await NeuronPurchaseReceipt.find({ user: USER_ID })
    .select({
      _id: 0,
      purchaseDate: 1,
      productId: 1,
      currency: 1,
      price: 1,
    })
    .lean();

const getBoostPurchases = async (USER_ID) =>
  await BoostPurchaseReceipt.find({ user: USER_ID })
    .select({
      _id: 0,
      purchaseDate: 1,
      productId: 1,
      currency: 1,
      price: 1,
      purchasedFrom: 1,
    })
    .lean();

const getWebPurchases = async (USER_ID) =>
  await StripeReceipt.find({ user: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      product: 1,
      currency: 1,
      price: 1,
    })
    .lean();

const getAdsWatched = async (USER_ID) =>
  await AdReceipt.find({ user: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      rewardItem: 1,
      rewardAmount: 1,
    })
    .lean();

const getHideList = async (USER_ID) =>
  await HideList.find({ userId: USER_ID })
    .select({
      _id: 0,
      emailsList: 1,
      phonesList: 1,
    })
    .lean();

const getPersonalityQuizHistory = async (USER_ID) =>
  await PersonalityQuizResult.find({ user: USER_ID })
    .select({
      _id: 0,
      date: 1,
      quiz: 1,
      mbti: 1,
    })
    .lean();

const getNotifications = async (USER_ID) =>
  await Notification.find({ user: USER_ID })
    .select({
      _id: 0,
      updatedAt: 1,
      postType: 1,
      notificationType: 1,
      seen: 1,
      numProfiles: 1,
    })
    .lean();

const getCoinTransactions = async (USER_ID) =>
  await CoinTransaction.find({ user: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      transactionAmount: 1,
      newBalance: 1,
      description: 1,
    })
    .lean();

const getAccountDeleteAttempts = async (USER_ID) =>
  await DeleteAccountAttempt.find({ user: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      feedback: 1,
      reason: 1,
    })
    .lean();

const getSavedPosts = async (USER_ID) =>
  await SavedQuestion.find({ user: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
    })
    .lean();

const getAiUsage = async (USER_ID) =>
  await OpenaiTransaction.find({ user: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      useCase: 1,
      useCaseSubtype: 1,
      useCaseSubtype2: 1,
      outputLanguage: 1,
    })
    .lean();

const getPostViews = async (USER_ID) =>
  await QuestionViewData.find({ user: USER_ID })
    .select({
      _id: 0,
      updatedAt: 1,
      numClicks: 1,
      numSecondsReadingOnFeed: 1,
      numSecondsOnPost: 1,
      numSecondsReadingComments: 1,
      numShares: 1,
    })
    .lean();

const getSwipes = async (USER_ID) =>
  await Action.find({ from: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      like: 1,
      pass: 1,
      block: 1,
      hide: 1,
    })
    .lean();

const getBlockList = async (USER_ID) =>
  await Block.find({ from: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
    })
    .lean();

const getFollows = async (USER_ID) =>
  await Follow.find({ from: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      approved: 1,
    })
    .lean();

const getHiddenSocial = async (USER_ID) =>
  await HideOnSocial.find({ from: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
    })
    .lean();

const getProfileViews = async (USER_ID) =>
  await ProfileView.find({ from: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
    })
    .lean();

const getPersonalityVotes = async (USER_ID) =>
  await ProfileVote.find({ from: USER_ID })
    .select({
      _id: 0,
      mbti: 1,
      enneagram: 1,
      horoscope: 1,
    })
    .lean();

const getStoriesViewed = async (USER_ID) =>
  await Story.find({ usersWhoViewed: USER_ID })
    .select({
      _id: 0,
      storyCreatedAt: '$createdAt',
    })
    .lean();

const getStoriesLiked = async (USER_ID) =>
  await Story.find({ usersWhoLiked: USER_ID })
    .select({
      _id: 0,
      storyCreatedAt: '$createdAt',
    })
    .lean();

const getPostsLiked = async (USER_ID) =>
  await Question.find({ usersThatLiked: USER_ID })
    .select({
      _id: 0,
      postCreatedAt: '$createdAt',
    })
    .lean();

const getLikedComments = async (USER_ID) =>
  await Comment.find({ usersThatLiked: USER_ID })
    .select({
      _id: 0,
      commentCreatedAt: '$createdAt',
    })
    .lean();

const getAwardsGiven = async (USER_ID) =>
  await Award.find({ sender: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      postType: 1,
      award: 1,
      anonymous: 1,
    })
    .lean();

const getBoostsUsed = async (USER_ID) =>
  await BoostMetric.find({ user: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      durationMinutes: 1,
      boostExpiration: 1,
    })
    .lean();

const getInterestsSubmitted = async (USER_ID) =>
  await Interest.find({ createdBy: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      name: 1,
      status: 1,
      pending: 1,
    })
    .lean();

const getVerificationAttempts = async (USER_ID) =>
  await LivenessChallenge.find({ user: USER_ID })
    .select({
      _id: 0,
      date: 1,
      frames: 1,
    })
    .lean();

const getQodSubmissions = async (USER_ID) =>
  await QuestionCandidate.find({ createdBy: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      status: 1,
      text: 1,
      isAnonymous: 1,
      language: 1,
    })
    .lean();

const getReferrals = async (USER_ID) =>
  await Referral.find({ referredBy: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
    })
    .lean();

const getReportsMade = async (USER_ID) =>
  await Report.find({ reportedBy: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      reason: 1,
      comment: 1,
    })
    .lean();

const getReportsReceived = async function(USER_ID) {
  const reports = await Report.find({ reportedUser: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      reason: 1,
      status: 1,
      reportedBy: 1,
    })
    .lean();

  for (const report of reports) {
    if (!report.reportedBy) {
      report.reason = ['high_risk'];
    } else {
      report.reason = report.reason.map(function(x) {
        if (['Catfish/Scammer','Underage','Spam','Inappropriate Messages','Inappropriate Profile','Other'].includes(x)) {
          return x;
        }
        return 'high_risk';
      });
    }
    report.reportedBy = undefined;
  }

  return reports;
}

const getPostReports = async (USER_ID) =>
  await PostReport.find({ reportedBy: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      reason: 1,
      explanation: 1,
    })
    .lean();

const getTranslationSuggestions = async (USER_ID) =>
  await Translation.find({ createdBy: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      currentTranslation: 1,
      correctTranslation: 1,
      details: 1,
      language: 1,
      refImage: 1,
    })
    .lean();

const processPendingRequest = async (dataRequest, dryRun) => {
  const startTime = Date.now();
  const USER_ID = dataRequest.userId;
  const zipName = `${USER_ID}_${Date.now()}.zip`;
  const dir = `data`;
  const mediaDir = path.join(dir, 'media');
  try {
    await fs.rm(dir, { recursive: true, force: true });
    await fs.mkdir(dir, { recursive: true });
    await fs.mkdir(mediaDir, { recursive: true });
    let numMediaFiles = 0;
    const user = await getProfileAndAccount(USER_ID);
    const chats = await getChatsAndMessages(USER_ID);
    const posts = await getPosts(USER_ID);
    const comments = await getComments(USER_ID);
    const stories = await getStories(USER_ID);
    const boo_infinity_purchases = await getInfinityPurchases(USER_ID);
    const coin_purchases = await getCoinPurchases(USER_ID);
    const super_love_purchases = await getSuperLikePurchases(USER_ID);
    const neuron_purchases = await getNeuronPurchases(USER_ID);
    const boost_purchases = await getBoostPurchases(USER_ID);
    const web_purchases = await getWebPurchases(USER_ID);
    const ads_watched = await getAdsWatched(USER_ID);
    const hide_list = await getHideList(USER_ID);
    const personality_quiz_history = await getPersonalityQuizHistory(USER_ID);
    const notifications = await getNotifications(USER_ID);
    const coin_transactions = await getCoinTransactions(USER_ID);
    const delete_account_attempts = await getAccountDeleteAttempts(USER_ID);
    const saved_posts = await getSavedPosts(USER_ID);
    const boo_ai_usage = await getAiUsage(USER_ID);
    const post_views = await getPostViews(USER_ID);
    const swipes = await getSwipes(USER_ID);
    const blocks = await getBlockList(USER_ID);
    const follows = await getFollows(USER_ID);
    const hidden_on_social = await getHiddenSocial(USER_ID);
    const profile_views = await getProfileViews(USER_ID);
    const personality_database_votes = await getPersonalityVotes(USER_ID);
    const stories_i_viewed = await getStoriesViewed(USER_ID);
    const stories_i_liked = await getStoriesLiked(USER_ID);
    const posts_i_liked = await getPostsLiked(USER_ID);
    const comments_i_liked = await getLikedComments(USER_ID);
    const awards_given = await getAwardsGiven(USER_ID);
    const boosts_used = await getBoostsUsed(USER_ID);
    const interests_submitted = await getInterestsSubmitted(USER_ID);
    const verification_attempts = await getVerificationAttempts(USER_ID);
    const question_of_the_day_submissions = await getQodSubmissions(USER_ID);
    const referrals = await getReferrals(USER_ID);
    const reports_made = await getReportsMade(USER_ID);
    const reports_received = await getReportsReceived(USER_ID);
    const postReports = await getPostReports(USER_ID);
    const translation_suggestions = await getTranslationSuggestions(USER_ID);
    const stripe_data_export = await getStripeDataExport(user.stripeCustomerId);

    {
      if (user.audioDescription) {
        user.audioDescription = await downloadFile(mediaDir, user.audioDescription, numMediaFiles++, USER_ID);
      }
      if (user.pictures && user.pictures.length) {
        for (let i = 0; i < user.pictures.length; i++) {
          user.pictures[i] = await downloadFile(mediaDir, user.pictures[i], numMediaFiles++, USER_ID);
        }
      }
      if (user.verification.pictures && user.verification.pictures.length) {
        for (let i = 0; i < user.verification.pictures.length; i++) {
          user.verification.pictures[i] = await downloadFile(mediaDir, user.verification.pictures[i], numMediaFiles++, USER_ID);
        }
      }
      await fs.writeFile(path.join(dir, 'profile_and_account.json'), JSON.stringify(user, null, 2));
    }
    {
      for (let i = 0; i < chats.length; i++) {
        let chat = chats[i];
        chats[i].mutePostNotifications = chat && chat.dndPostFrom ? chat.dndPostFrom.filter((id) => USER_ID !== id).length > 0 : false;
        chats[i].muteMessageNotifications = chat && chat.dndMessageFrom ? chat.dndMessageFrom.filter((id) => USER_ID !== id).length > 0 : false;
        delete chats[i].dndPostFrom;
        delete chats[i].dndMessageFrom;
        if (chat.messagesSentByMe && chat.messagesSentByMe.length) {
          for (let j = 0; j < chat.messagesSentByMe.length; j++) {
            if (chat.messagesSentByMe[j].unsent) {
              // Clear specified properties using Object.assign
              Object.assign(chat.messagesSentByMe[j], {
                text: undefined,
                image: undefined,
                aspectRatio: undefined,
                gif: undefined,
                audio: undefined,
                audioWaveform: undefined,
                audioDuration: undefined,
                video: undefined,
              });
            }
            if (chat.messagesSentByMe[j].image) {
              chat.messagesSentByMe[j].image = await downloadFile(mediaDir, chat.messagesSentByMe[j].image, numMediaFiles++, USER_ID);
            }
            if (chat.messagesSentByMe[j].audio) {
              chat.messagesSentByMe[j].audio = await downloadFile(mediaDir, chat.messagesSentByMe[j].audio, numMediaFiles++, USER_ID);
            }
            if (chat.messagesSentByMe[j].video) {
              chat.messagesSentByMe[j].video = await downloadFile(mediaDir, chat.messagesSentByMe[j].video, numMediaFiles++, USER_ID);
            }
          }
        }
      }
      await fs.writeFile(path.join(dir, 'chats_and_messages.json'), JSON.stringify(chats, null, 2));
    }
    {
      for (let i = 0; i < posts.length; i++) {
        if (posts[i].image) {
          posts[i].image = await downloadFile(mediaDir, posts[i].image, numMediaFiles++, USER_ID);
        }
        if (posts[i].audio) {
          posts[i].audio = await downloadFile(mediaDir, posts[i].audio, numMediaFiles++, USER_ID);
        }
      }
      await fs.writeFile(path.join(dir, 'posts.json'), JSON.stringify(posts, null, 2));
    }
    {
      for (let i = 0; i < comments.length; i++) {
        if (comments[i].image) {
          comments[i].image = await downloadFile(mediaDir, comments[i].image, numMediaFiles++, USER_ID);
        }
        if (comments[i].audio) {
          comments[i].audio = await downloadFile(mediaDir, comments[i].audio, numMediaFiles++, USER_ID);
        }
      }
      await fs.writeFile(path.join(dir, 'comments.json'), JSON.stringify(comments, null, 2));
    }
    {
      for (let i = 0; i < stories.length; i++) {
        if (stories[i].image) {
          stories[i].image = await downloadFile(mediaDir, stories[i].image, numMediaFiles++, USER_ID);
        }
      }
      await fs.writeFile(path.join(dir, 'stories.json'), JSON.stringify(stories, null, 2));
    }
    {
      for (let i = 0; i < verification_attempts.length; i++) {
        if (verification_attempts[i].frames && verification_attempts[i].frames.length) {
          for (let j = 0; j < verification_attempts[i].frames.length; j++) {
            verification_attempts[i].frames[j].key = await downloadFile(mediaDir, verification_attempts[i].frames[j].key, numMediaFiles++, USER_ID);
          }
        }
      }
      await fs.writeFile(path.join(dir, 'verification_attempts.json'), JSON.stringify(verification_attempts, null, 2));
    }
    {
      for (let i = 0; i < translation_suggestions.length; i++) {
        if (translation_suggestions[i].refImage) {
          translation_suggestions[i].refImage = await downloadFile(mediaDir, translation_suggestions[i].refImage, numMediaFiles++, USER_ID);
        }
      }
      await fs.writeFile(path.join(dir, 'translation_suggestions.json'), JSON.stringify(translation_suggestions, null, 2));
    }

    /* -------------------------- */
    // California notice

    if ((user.ipData?.region == 'CA' && user.ipData?.countryCode == 'US') || (user.state == 'California' && user.country == 'United States')) {
      log(`userId: ${USER_ID} - Processing California notice for ${user.email}`);

      const attemptedVerification = user.verification?.pictures?.length || verification_attempts?.length;

      const category_a_identifiers = [];
      const category_b_customerRecords = [];
      const category_c_protectedClass = [];
      const category_d_commercial = [];
      const category_e_biometric = [];
      const category_f_electronic = [];
      const category_g_geolocation = [];
      const category_h_audiovisual = [];
      const category_i_professional = [];
      const category_j_education = [];
      const category_k_inferences = [];
      const category_spi = [];

      // category a
      if (user.firstName) {
        category_a_identifiers.push('name');
      }
      if (user.email) {
        category_a_identifiers.push('email address');
      }
      if (user.phoneNumber) {
        category_a_identifiers.push('phone number');
      }
      if (user.ipData?.ip) {
        category_a_identifiers.push('IP addresses');
      }
      if (user.deviceId) {
        category_a_identifiers.push('device ID');
      }

      // category b
      if (stripe_data_export?.stripe?.payment_methods?.length) {
        category_b_customerRecords.push('credit‑card last 4 digits');
        category_b_customerRecords.push('billing address');
      }
      if (user.education || user.work) {
        category_b_customerRecords.push('education/employment');
      }

      // category c
      if (user.gender) {
        category_c_protectedClass.push('gender');
      }
      if (user.ethnicities?.length) {
        category_c_protectedClass.push('ethnicity');
      }
      if (user.age) {
        category_c_protectedClass.push('age');
      }
      if (user.sexuality || (user.gender && user.preferences?.dating?.length)) {
        category_c_protectedClass.push('sexual orientation');
      }

      // category d
      if (boo_infinity_purchases?.length || coin_purchases?.length || super_love_purchases?.length || neuron_purchases?.length || boost_purchases?.length || web_purchases?.length) {
        category_d_commercial.push('purchase history');
      }

      // category e
      if (attemptedVerification) {
        category_e_biometric.push('processing of biometric information for unique identification');
      }

      // category f
      category_f_electronic.push('app usage');
      if (user.appsflyer) {
        category_f_electronic.push('information about your interaction with our advertisements (e.g., acquisition channel, campaign name)');
      }

      // category g
      if (user.ipData?.city || user.ipData?.region || user.ipData?.country || user.ipData?.location) {
        category_g_geolocation.push('approximate location from IP');
      }
      if (user.location) {
        category_g_geolocation.push('precise geolocation');
      }

      // category h
      if (user.pictures || user.audioDescription) {
        category_h_audiovisual.push('user-uploaded images, videos, and audio recordings');
      }

      // category i
      if (user.work) {
        category_i_professional.push('user-provided “work” field on profile');
      }

      // category j
      // none

      // category k
      // todo
      if (reports_received?.length) {
        category_k_inferences.push('trust & safety risk flag');
      }

      // category sensitive personal information (spi)
      if (attemptedVerification) {
        category_spi.push('processing of biometric information for unique identification');
      }
      if (user.location) {
        category_spi.push('precise geolocation');
      }

      // sources
      const sources = [
        'The consumer directly – information you submitted (account setup, etc).',
        'Your device/browser (via our sites/apps) – telemetry we collected when you used our services.',
      ];
      if (user.appsflyer) {
        sources.push('Data analytics providers — attribution reports linking your install/session to a campaign.');
      }
      if (user.stripeCustomerId) {
        sources.push('Service providers/contractors (collecting on our behalf) – Stripe collected billing information related to your purchases.');
      }
      if (boo_infinity_purchases?.length || coin_purchases?.length || super_love_purchases?.length || neuron_purchases?.length || boost_purchases?.length) {
        sources.push('Apple App Store / Google Play sent us purchase receipts for your in‑app orders.');
      }

      function formatExamples(examples) {
        return examples.join('; ') || 'none';
      }

      const section4 = `
4  Categories of Third Parties to Whom We Disclosed, Sold, or Shared Your PI
  * 4.1  Matrix of Disclosures for a Business Purpose
    * Cloud hosting & storage (service providers) — host/store our databases and files.
      * PI disclosed: Exactly the PI categories listed for you in Section 1 (i.e., the categories we actually collected about you are hosted with our cloud provider)
${ user.stripeCustomerId ? `
    * Payment processors (service providers) — process web payments.
      * PI disclosed: A. Identifiers (name, email)${stripe_data_export?.stripe?.payment_methods?.length ? '; B. § 1798.80(e) (e.g., billing address)' : ''}${web_purchases?.length ? '; D. Commercial information (e.g., transaction details)' : ''}
` : '' }
    * Customer support platforms (service providers) — handle our support inbox/tickets.
      * PI disclosed: A. Identifiers (name, email)
    * Messaging & email‑delivery (service providers) — send our emails.
      * PI disclosed: A. Identifiers (name, email)
  * 4.2  Categories of PI Sold (monetary or other valuable consideration)
    * None
  * 4.3  Categories of PI Shared for Cross‑Context Behavioural Advertising
    * None
`.replace(/\n{2,}/g, '\n').trim();   // collapses consecutive blank lines into a single newline and trims trailing/leading newlines

      const final_text = `
1  Categories of Personal Information We Collected About You
  * A. Identifiers - ${formatExamples(category_a_identifiers)}
  * B. Personal information in § 1798.80(e) - ${formatExamples(category_b_customerRecords)}
  * C. Protected‑class characteristics - ${formatExamples(category_c_protectedClass)}
  * D. Commercial information - ${formatExamples(category_d_commercial)}
  * E. Biometric information - ${formatExamples(category_e_biometric)}
  * F. Internet / electronic network activity - ${formatExamples(category_f_electronic)}
  * G. Geolocation data - ${formatExamples(category_g_geolocation)}
  * H. Audio/electronic/visual/thermal/olfactory/similar - ${formatExamples(category_h_audiovisual)}
  * I. Professional or employment‑related - ${formatExamples(category_i_professional)}
  * J. Nonpublic education information (FERPA) - ${formatExamples(category_j_education)}
  * K. Inferences - ${formatExamples(category_k_inferences)}
  * Sensitive Personal Information (SPI) - ${formatExamples(category_spi)}

2  Categories of Sources
${sources.map(x => '  * '.concat(x)).join('\n')}

3  Business or Commercial Purposes for Collection / Use / Sharing
  * Performing services - Maintain/serve your account; fulfill orders/transactions; verify customer information; process payments via our service providers; provide analytics and storage/hosting for our services.
  * Research & Development - Test and evaluate new features and UX.
  * Quality & Safety - Verify or maintain the quality or safety of our service/device, and improve, upgrade, or enhance it.
  * Security & Integrity - Detect and investigate security incidents; protect against malicious, deceptive, fraudulent, or illegal activity; ensure physical safety where applicable.
  * Debugging - Use app/device telemetry and error logs to identify and repair errors that impair existing intended functionality.

${section4}
`

      await fs.writeFile(path.join(dir, 'ccpa_cpra_access_response.txt'), final_text);
    }

    /* -------------------------- */

    await fs.writeFile(path.join(dir, 'boo_infinity_purchases.json'), JSON.stringify(boo_infinity_purchases, null, 2));
    await fs.writeFile(path.join(dir, 'coin_purchases.json'), JSON.stringify(coin_purchases, null, 2));
    await fs.writeFile(path.join(dir, 'super_love_purchases.json'), JSON.stringify(super_love_purchases, null, 2));
    await fs.writeFile(path.join(dir, 'neuron_purchases.json'), JSON.stringify(neuron_purchases, null, 2));
    await fs.writeFile(path.join(dir, 'boost_purchases.json'), JSON.stringify(boost_purchases, null, 2));
    await fs.writeFile(path.join(dir, 'web_purchases.json'), JSON.stringify(web_purchases, null, 2));
    await fs.writeFile(path.join(dir, 'ads_watched.json'), JSON.stringify(ads_watched, null, 2));
    await fs.writeFile(path.join(dir, 'hide_list.json'), JSON.stringify(hide_list, null, 2));
    await fs.writeFile(path.join(dir, 'personality_quiz_history.json'), JSON.stringify(personality_quiz_history, null, 2));
    await fs.writeFile(path.join(dir, 'notifications.json'), JSON.stringify(notifications, null, 2));
    await fs.writeFile(path.join(dir, 'coin_transactions.json'), JSON.stringify(coin_transactions, null, 2));
    await fs.writeFile(path.join(dir, 'delete_account_attempts.json'), JSON.stringify(delete_account_attempts, null, 2));
    await fs.writeFile(path.join(dir, 'saved_posts.json'), JSON.stringify(saved_posts, null, 2));
    await fs.writeFile(path.join(dir, 'boo_ai_usage.json'), JSON.stringify(boo_ai_usage, null, 2));
    await fs.writeFile(path.join(dir, 'post_views.json'), JSON.stringify(post_views, null, 2));
    await fs.writeFile(path.join(dir, 'swipes.json'), JSON.stringify(swipes, null, 2));
    await fs.writeFile(path.join(dir, 'blocks.json'), JSON.stringify(blocks, null, 2));
    await fs.writeFile(path.join(dir, 'follows.json'), JSON.stringify(follows, null, 2));
    await fs.writeFile(path.join(dir, 'hidden_on_social.json'), JSON.stringify(hidden_on_social, null, 2));
    await fs.writeFile(path.join(dir, 'profile_views.json'), JSON.stringify(profile_views, null, 2));
    await fs.writeFile(path.join(dir, 'personality_database_votes.json'), JSON.stringify(personality_database_votes, null, 2));
    await fs.writeFile(path.join(dir, 'stories_i_viewed.json'), JSON.stringify(stories_i_viewed, null, 2));
    await fs.writeFile(path.join(dir, 'stories_i_liked.json'), JSON.stringify(stories_i_liked, null, 2));
    await fs.writeFile(path.join(dir, 'posts_i_liked.json'), JSON.stringify(posts_i_liked, null, 2));
    await fs.writeFile(path.join(dir, 'comments_i_liked.json'), JSON.stringify(comments_i_liked, null, 2));
    await fs.writeFile(path.join(dir, 'awards_given.json'), JSON.stringify(awards_given, null, 2));
    await fs.writeFile(path.join(dir, 'boosts_used.json'), JSON.stringify(boosts_used, null, 2));
    await fs.writeFile(path.join(dir, 'interests_submitted.json'), JSON.stringify(interests_submitted, null, 2));
    await fs.writeFile(path.join(dir, 'question_of_the_day_submissions.json'), JSON.stringify(question_of_the_day_submissions, null, 2));
    await fs.writeFile(path.join(dir, 'referrals.json'), JSON.stringify(referrals, null, 2));
    await fs.writeFile(path.join(dir, 'reports_made.json'), JSON.stringify(reports_made, null, 2));
    await fs.writeFile(path.join(dir, 'reports_received.json'), JSON.stringify(reports_received, null, 2));
    await fs.writeFile(path.join(dir, 'post_reports.json'), JSON.stringify(postReports, null, 2));
    await fs.writeFile(path.join(dir, 'stripe_data_export.json'), JSON.stringify(stripe_data_export, null, 2));

    const zip = new AdmZip();
    zip.addLocalFolder(dir);
    zip.writeZip(zipName);

    if (!dryRun) {
      const result = await uploadToS3(zipName);

      // result.key = 'data-export/${Date.now()}.zip'
      let signedUrl = cfsign.getSignedUrl(
        `${process.env.WEB_DOMAIN}/${result.key}`,
        getSigningParams(7),
      );

      if (process.env.TESTING) {
        signedUrl = 'MOCK_SIGNED_URL';
      }

      await sendEmail(user, signedUrl, USER_ID);

      dataRequest.status = 'completed';
      dataRequest.downloadKey = result.key;
      dataRequest.emailedAt = new Date();
    }
  } catch (error) {
    log(`userId: ${dataRequest.userId} - Error while exporting data: ${error.message}`);
    if (!dryRun) {
      dataRequest.retryCount += 1;
      dataRequest.status = dataRequest.retryCount >= config.retryLimit ? 'failed' : 'pending';
      if (dataRequest.status === 'failed' || process.env.TESTING) {
        dataRequest.error = stringify(error);
      }
    }
  } finally {
    const durationSec = Number(((Date.now() - startTime) / 1000).toFixed(2));
    dataRequest.processingTimeSec = durationSec;
    await dataRequest.save();
    log(`userId: ${dataRequest.userId} - Data export ${dataRequest.status} in ${durationSec} seconds`);

    if (!dryRun) {
      await fs.rm(dir, { recursive: true, force: true });
      await fs.rm(zipName, { force: true });
    }
  }
};

const exportData = async (req, res, next) => {
  // always return 200 immediately: worker environment in AWS Elastic Beanstalk has a timeout
  if (!process.env.TESTING) {
    res.json({});
  }

  if (dataExportRunning) {
    log('Data export job already running, skipping this job');
    return;
  }

  dataExportRunning = true;
  try {
    const pendingDataRequests = await DataRequestHistory.find({ status: 'pending' });
    for (const dataRequest of pendingDataRequests) {
      await processPendingRequest(dataRequest);
    }
  } catch (error) {
    log(`Fatal error exporting data: ${error.message}`);
  } finally {
    dataExportRunning = false;
    if (process.env.TESTING) {
      // for test environments, return at end
      res.json({});
    }
  }
};

module.exports = {
  processPendingRequest,
  exportData,
};
