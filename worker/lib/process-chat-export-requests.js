const fs = require('fs').promises;
const syncFs = require('fs');
const path = require('path');
const AdmZip = require('adm-zip');
const cfsign = require('aws-cloudfront-sign');
const { getSigningParams } = require('../../lib/cloudfront');
const { s3, AWS_S3_BUCKET } = require('../../lib/s3');
const { sendEmailTemplateSES, translateEmail } = require('../../lib/email');
const User = require('../../models/user');
const Chat = require('../../models/chat');
const Message = require('../../models/message');
const ChatExportHistory = require('../../models/chat-export-history');
const { sendAutomatedReply } = require('../../lib/chat');
const { stringify } = require('flatted');

const log = (msg) => console.log(`[chat-export-requests]: ${msg}`);

const config = {
  retryLimit: 3,
  bucketName: process.env.AWS_EXPIRY_S3_BUCKET || 'MOCK_S3_EXPIRY_BUCKET',
};

let chatExportRunning = false;

const downloadFile = async (directory, key, mediaId, chatId) => {
  const fileName = `${mediaId}${path.extname(key)}`;
  const filePath = path.join(directory, fileName);

  try {
    const s3Stream = s3.getObject({ Bucket: AWS_S3_BUCKET, Key: key }).createReadStream();
    const fileStream = syncFs.createWriteStream(filePath);

    await new Promise((resolve, reject) => {
      s3Stream
        .on('error', reject)
        .pipe(fileStream)
        .on('error', reject)
        .on('finish', resolve);
    });

    return fileName;
  } catch (err) {
    log(`chatId ${chatId}: Error downloading file ${key}: ${err.message}`);
    try { await fs.unlink(filePath); } catch (_) {} // Cleanup partial file
    return 'Not found';
  }
};

const uploadToS3 = async (zipFilePath) => {
  if (process.env.TESTING) {
    return { key: `${Date.now()}.zip`, url: 'MOCK_URL' };
  }

  return new Promise((resolve, reject) => {
    const readStream = syncFs.createReadStream(zipFilePath);
    const params = {
      Bucket: config.bucketName,
      Key: `data-export/chat-${Date.now()}.zip`,
      Body: readStream,
    };

    s3.upload(params, (err, data) => {
      if (err) return reject(err);
      resolve({ key: data.Key, url: data.Location });
    });

    readStream.on('error', (err) => {
      reject(err);
    });
  });
};

const sendEmail = async (user, otherName, signedUrl) => {
  const template = 'chat-download-v1';
  const emailContent = [
    {
      name: 'GREETINGS',
      content: translateEmail({ phrase: 'Hello {{name}}', locale: user.locale }, { name: user.firstName }),
    },
    {
      name: 'MESSAGE',
      content: translateEmail({
        phrase: `We have compiled all the information associated with your conversation with {{other_name}}. You can download it using the link below, valid for 7 days. If you have any questions, please reach out to us.`,
        locale: user.locale,
      }, { other_name: otherName }),
    },
    {
      name: 'DOWNLOAD_NOW',
      content: translateEmail({ phrase: 'Download Data', locale: user.locale }),
    },
    {
      name: 'DOWNLOAD_LINK',
      content: signedUrl,
    },
    {
      name: 'THANK_YOU',
      content: translateEmail({ phrase: 'Love,', locale: user.locale }),
    },
    {
      name: 'TEAM_BOO',
      content: translateEmail({ phrase: 'The Boo Team', locale: user.locale }),
    },
  ];
  if (user.versionAtLeast('1.13.64')) {
    const dataDownloadLink = user.versionAtLeast('1.13.73') ? `[Download Data](${signedUrl})` : signedUrl;
    const messageText = `${translateEmail({ phrase: `We have compiled all the information associated with your conversation with {{other_name}}. You can download it using the link below, valid for 7 days.`, locale: user.locale }, { other_name: otherName })}\n\n${dataDownloadLink}`;
    await sendAutomatedReply(user, messageText);
  } else {
    await sendEmailTemplateSES(user, template, 'Your Chat Export is Now Available', emailContent);
  }
};

const getMessages = async (chat) =>
  await Message.aggregate([
    {
      $match: {
        chat: chat._id,
      },
    },
    {
      $sort: {
        createdAt: 1,
      },
    },
    {
      $project: {
        _id: 0,
        createdAt: 1,
        sender: 1,
        text: 1,
        gif: 1,
        image: 1,
        audio: 1,
        video: 1,
        unsent: 1,
      },
    },
  ]);

const processPendingRequest = async (dataRequest) => {
  const startTime = Date.now();
  const zipFolder = `${Date.now()}-${dataRequest.chatId}.zip`;
  const dir = 'chat';
  const mediaDir = path.join(dir, 'media');
  try {
    await fs.rm(dir, { recursive: true, force: true });
    await fs.mkdir(dir, { recursive: true });
    await fs.mkdir(mediaDir, { recursive: true });

    let chat = await Chat.findById(dataRequest.chatId);
    if (chat && !chat.deletedAt) {
      const messages = await getMessages(chat);
      log(`chatId ${dataRequest.chatId}: Found ${messages.length} messages`);

      const USER_1 = await User.findById(chat.users[0]);
      const USER_2 = await User.findById(chat.users[1]);

      for (let j = 0; j < messages.length; j++) {
        if (messages[j].sender === USER_1._id) {
          messages[j].sender = USER_1.firstName;
        } else if (messages[j].sender === USER_2.id) {
          messages[j].sender = USER_2.firstName;
        } else {
          log(`chatId ${dataRequest.chatId}: Message sender does not match either user!`);
        }

        messages[j].text = messages[j].text.replace('[update to latest version to view]', '').trim();

        if (messages[j].unsent) {
          Object.assign(messages[j], {
            text: undefined,
            gif: undefined,
            image: undefined,
            audio: undefined,
            video: undefined,
          });
        }

        if (messages[j].image) {
          messages[j].image = await downloadFile(mediaDir, messages[j].image, j, dataRequest.chatId);
        }
        if (messages[j].audio) {
          messages[j].audio = await downloadFile(mediaDir, messages[j].audio, j, dataRequest.chatId);
        }
        if (messages[j].video) {
          messages[j].video = await downloadFile(mediaDir, messages[j].video, j, dataRequest.chatId);
        }
      }
      await fs.writeFile(path.join(dir, 'messages.json'), JSON.stringify(messages, null, 2));

      const zip = new AdmZip();
      zip.addLocalFolder(dir);
      zip.writeZip(zipFolder);
      const result = await uploadToS3(zipFolder);

      // result.key = 'data-export/chat-${Date.now()}.zip'
      let signedUrl = cfsign.getSignedUrl(
        `${process.env.WEB_DOMAIN}/${result.key}`,
        getSigningParams(7),
      );

      if (process.env.TESTING) {
        signedUrl = 'MOCK_SIGNED_URL';
      }

      await sendEmail(USER_1, USER_2.firstName, signedUrl);
      await sendEmail(USER_2, USER_1.firstName, signedUrl);

      dataRequest.status = 'completed';
      dataRequest.downloadKey = result.key;
      dataRequest.emailedAt = new Date();
    } else {
      log(`chatId ${dataRequest.chatId}: Could not find chat or chat deleted`);
      dataRequest.status = 'completed';
    }
  } catch (error) {
    log(`chatId ${dataRequest.chatId}: Error while exporting data: ${error.message}`);
    dataRequest.retryCount += 1;
    dataRequest.status = dataRequest.retryCount >= config.retryLimit ? 'failed' : dataRequest.status;
    if (dataRequest.status === 'failed' || process.env.TESTING) {
      dataRequest.error = stringify(error);
    }
  } finally {
    const durationSec = Number(((Date.now() - startTime) / 1000).toFixed(2));
    dataRequest.processingTimeSec = durationSec;
    await dataRequest.save();
    log(`chatId ${dataRequest.chatId}: Chat export ${dataRequest.status} in ${durationSec} seconds`);

    await fs.rm(dir, { recursive: true, force: true });
    await fs.rm(zipFolder, { force: true });
  }
};

const exportChats = async (req, res, next) => {
  // always return 200 immediately: worker environment in AWS Elastic Beanstalk has a timeout
  if (!process.env.TESTING) {
    res.json({});
  }

  if (chatExportRunning) {
    log('Chat export job already running, skipping this job');
    return;
  }

  chatExportRunning = true;
  try {
    const approvedRequests = await ChatExportHistory.find({ status: 'approved' });
    for (const exportRequest of approvedRequests) {
      await processPendingRequest(exportRequest);
    }
  } catch (error) {
    log(`Fatal error exporting chat: ${error.message}`);
  } finally {
    chatExportRunning = false;
    if (process.env.TESTING) {
      // for test environments, return at end
      res.json({});
    }
  }
};

module.exports = { exportChats };
