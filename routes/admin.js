const mongoose = require('mongoose');
const express = require('express');

const router = express.Router();
const asyncHandler = require('express-async-handler');
const countryLookup = require('country-code-lookup');
const usStatesLookup = require('us-state-codes');
const moment = require('moment');
const { DateTime } = require('luxon');
const { ObjectId } = require('mongoose').Types;
const {
  notFoundError, forbiddenError, invalidInputError, conflictError, applicationError,
} = require('../lib/http-errors');
const User = require('../models/user');
const LivenessChallenge = require('../models/liveness-challenge');
const PhoneUpdateLogs = require('../models/phone-update-logs');
const UserMetadata = require('../models/user-metadata');
const AdminSearch = require('../models/admin-search');
const Action = require('../models/action');
const Block = require('../models/block');
const Chat = require('../models/chat');
const Message = require('../models/message');
const Report = require('../models/report');
const Question = require('../models/question');
const Comment = require('../models/comment');
const Interest = require('../models/interest');
const BannedSource = require('../models/banned-source');
const BanAppeal = require('../models/ban-appeal');
const ProfileTempBanAppeal = require('../models/profile-temp-ban-appeal');
const TempBanAppeal = require('../models/temp-ban-appeal');
const { findOtherUser } = require('../middleware/user');
const locationLib = require('../lib/location');
const socketLib = require('../lib/socket');
const { sendSocketEvent } = require('../lib/socket');
const { cloudwatch } = require('../lib/cloudwatch');
const { shadowBan, unban, tempBan, unbanDeletedUser, unBanAllUsersOnSameDevice } = require('../lib/report');
const s3 = require('../lib/s3');
const databaseLib = require('../lib/database');
const chatLib = require('../lib/chat');
const projections = require('../lib/projections');
const coinsConstants = require('../lib/coins-constants');
const coinsLib = require('../lib/coins');
const {
  getPosts, banPost, dismissPost, formatQuestion, formatComment, createQod,
} = require('../lib/social');
const { updateUserScore } = require('../lib/score');
const { sendNotification } = require('../config/firebase-admin');
const constants = require('../lib/constants');
const { pageSize, rejectionReasons } = require('../lib/constants');
const { formatMessage, getMessages } = require('../lib/message');
const { updatePhoneNumber, updateEmail } = require('../lib/update-firebase-user');
const { translate } = require('../lib/translate');
const QuestionCandidate = require('../models/question-candidate');
const { sendSupportUserReply, sendAutomatedReply } = require('../lib/chat');
const { languageCodes } = require('../lib/languages');
const Translation = require('../models/translation');
const PurchaseReceipt = require('../models/purchase-receipt');
const CoinPurchaseReceipt = require('../models/coin-purchase-receipt');
const CoinTransaction = require('../models/coin-transaction');
const BoostTransaction = require('../models/boost-transaction');
const SuperLikeTransaction = require('../models/super-like-transaction');
const SuperLikePurchaseReceipt = require('../models/super-like-purchase-receipt');
const NeuronPurchaseReceipt = require('../models/neuron-purchase-receipt');
const BoostPurchaseReceipt = require('../models/boost-purchase-receipt');
const PostReport = require('../models/post-report');
const MessageReport = require('../models/message-report');
const reportLib = require('../lib/report');
const ScammerCleanup = require('../models/scammer-cleanup');

const CANDIDATE_MAX_COUNT = (process.env.CANDIDATE_MAX_COUNT) || 100;
const Category = require('../models/category');
const Subcategory = require('../models/subcategory');
const Profile = require('../models/profile');
const ProfileCandidate = require('../models/profile-candidate');
const ProfileImageCandidate = require('../models/profile-image-candidate');
const InterestCountryCount = require('../models/interest-country-count')
const socialLib = require('../lib/social');
const userLib = require('../lib/user');
const BannedUser = require('../models/banned-user');
const SupportAgentAccuracy = require('../models/support-accuracy');
const { setFaceComparisonReferenceImage } = require('../lib/verification');
const emailLib = require('../lib/email');
const { createSlug } = require('../lib/url');
const profilesLib = require('../lib/profiles-v3');

const TRANSLATION_MAX_COUNT = (process.env.TRANSLATION_MAX_COUNT) || 100;
const TRANSLATION_REWARD = 25;
const DB_UPLOAD_COIN_REWARD = 10;
const DB_UPLOAD_KARMA_REWARD = 10;

const checkPermissions = function (permissionNames) {
  permissionNames = ['all'].concat(permissionNames);
  return function (req, res, next) {
    if (req.user.adminPermissions && permissionNames.some((x) => req.user.adminPermissions[x])) {
      return next();
    }
    return next(forbiddenError());
  };
};

function normalizeUserFields(user) {
  // karma must be rounded to an int prior to sending to frontend
  user.karma = Math.round(user.karma);
}

async function awardCoinsAndKarmaForDatabaseUpload(userId) {
  if (userId) {
    await coinsLib.updateCoins(
      { user: userId },
      {
        $inc: {
          coins: DB_UPLOAD_COIN_REWARD,
        },
      },
      'database upload reward',
    );
    await socialLib.incrementKarma(
      userId,
      DB_UPLOAD_KARMA_REWARD,
    );
  }
}

async function onUserDbUpload(userId) {
  if (!userId) {
    return;
  }
  await User.updateOne({
    _id: userId,
  }, {
    $inc: {
      numDbUploads: 1,
      dbUploadCoinsReceived: DB_UPLOAD_COIN_REWARD,
      dbUploadKarmaReceived: DB_UPLOAD_KARMA_REWARD,
    },
  });
  await awardCoinsAndKarmaForDatabaseUpload(userId);
}

async function getProfilesToVerify(status) {
  let users = [];

  while (users.length < constants.getVerifyProfilePageSize()) {
    let user = await User.findOneAndUpdate(
      {
        'verification.status': status,
        'verification.assignedToQueueAt': { $lt: moment().subtract(5, 'minutes').toDate() },
      },
      {
        'verification.assignedToQueueAt': new Date(),
      },
      {
        sort: { 'verification.assignedToQueueAt': 1 },
        fields: projections.userProfileAdminFieldsObj,
      },
    ).lean();
    if (!user) {
      break;
    }
    users.push(user);
  }

  while (users.length < constants.getVerifyProfilePageSize()) {
    let user = await User.findOneAndUpdate(
      {
        'verification.status': status,
        'verification.assignedToQueueAt': { $exists: false },
      },
      {
        'verification.assignedToQueueAt': new Date(),
      },
      {
        sort: { 'verification.updatedAt': 1 },
        fields: projections.userProfileAdminFieldsObj,
      },
    ).lean();
    if (!user) {
      break;
    }
    users.push(user);
  }
  users.forEach(user => normalizeUserFields(user));

  return users;
}

async function handleVerificationDecision(user, verified, rejectionReason, verifiedBy, additionalNote) {
  if (!user) {
    return;
  }
  if ((verified && user.verification.status == 'verified') || (!verified && user.verification.status == 'rejected')) {
    return;
  }

  const isFirstVerification = user.verification.status == 'pending';
  if (user.livenessVerification) {
    user.livenessVerification.manuallyCheckedBy = verifiedBy;
    user.livenessVerification.manuallyCheckedDate = Date.now();
    user.livenessVerification.manuallyCheckedResult = verified;
    user.livenessVerification.manuallyCheckedRejectionReason = rejectionReason;

    const challenge = await LivenessChallenge.findOne({ id: user.livenessVerification.id });
    if (challenge) {
      challenge.manuallyCheckedBy = verifiedBy;
      challenge.manuallyCheckedDate = Date.now();
      challenge.manuallyCheckedResult = verified;
      challenge.manuallyCheckedRejectionReason = rejectionReason;
      await challenge.save();
    }
  }

  if (verified) {
    user.verification.rejectionReason = undefined;
  }
  else if (rejectionReason !== undefined) {
    if (!rejectionReasons.includes(rejectionReason)) {
      rejectionReason = 'Photo unclear';
    }
    user.verification.rejectionReason = rejectionReason;
  }

  let additionalNoteFormatted = additionalNote ? ` (${additionalNote})` : '';
  await user.setVerificationStatus(verified ? 'verified' : 'rejected', `handled manually by admin${additionalNoteFormatted}`, verifiedBy);
  user.verification.verifiedBy = verifiedBy;
  user.verification.verifiedDate = Date.now();
  if (verified) {
    if (!user.verification.faceComparisonReferenceImage) {
      if (user.livenessVerification?.date > user.verification.pictureUploadedAt) {
        setFaceComparisonReferenceImage(user, user.livenessVerification.frames[0].key);
      } else {
        setFaceComparisonReferenceImage(user, user.verification.pictures.at(-1));
      }
    }
    user.verification.newReverificationSystem = userLib.useNewReverification(user);
  }
  await user.save();

  await updateUserScore(user, { verification: 1 });

  const data = {
    verificationStatus: user.verification.status,
    verified: user.verification.status == 'verified',
    rejectionReason: user.verification.rejectionReason,
  };

  sendSocketEvent(user._id, 'verification', data);

  const notifData = { verification: JSON.stringify(data) };

  if (verified) {
    await socketLib.grantVerifyProfileAward(user);
    sendNotification(
      user,
      null,
      translate('Verification Successful', user.locale),
      translate('Your profile has been verified.', user.locale),
      notifData,
      null,
      'general',
      'profile-verification-successful',
    );
    if (isFirstVerification && user.os == 'web') {
      await emailLib.sendVerificationSuccessfulEmail(user);
    }
  } else {
    if (user.verification.status == 'rejected' && user.versionAtLeast('1.13.30')) {
      userLib.notifyVerificationRejection(user);
    }
  }
}

async function incrementDecision(supportAgentId, supportAgentEmail, decision = 0, incorrect = 0) {
  if (!supportAgentId) {
    console.log('incrementDecision called without supportAgentId');
    return;
  }

  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const update = {
    $inc: {
      totalDecisions: decision,
      incorrectDecisions: incorrect,
    },
  };

  if (supportAgentEmail) {
    update.$setOnInsert = { supportAgentEmail };
  }

  await SupportAgentAccuracy.updateOne(
    { date: today, supportAgentId },
    update,
    { upsert: true },
  );
}

module.exports = function () {
  router.put('/ban', checkPermissions(['support', 'translator']), asyncHandler(findOtherUser), asyncHandler(async (req, res, next) => {

    if (req.user.adminPermissions.translator
      && !await Question.findOne({ createdBy: req.otherUser._id, language: req.user.adminPermissions.translator }, { _id: 1 })
      && !await Comment.findOne({ createdBy: req.otherUser._id, language: req.user.adminPermissions.translator }, { _id: 1 })
    ) {
      return next(forbiddenError());
    }

    // publish metrics to cloudwatch
    const reports = await Report.find({ reportedUser: req.otherUser._id, status: 'needs review' });
    for (const report of reports) {
      const waitTime = moment().diff(report.createdAt, 'seconds');
      const params = {
        MetricData: [
          {
            MetricName: 'ProfileReports',
            Value: waitTime,
            Unit: 'Seconds',
          },
        ],
        Namespace: `SupportTeamMetrics_${process.env.NODE_ENV}`,
      };
      await cloudwatch.putMetricData(params).promise();
    }

    const bannedReason = req.body.bannedReason || (req.user.adminPermissions.translator ? 'bannedByTranslator' : 'bannedByAdmin');
    await shadowBan(req.otherUser, req.uid, bannedReason, req.body.bannedNotes, {}, req.body.bannedReasons);
    await BannedSource.addToBanned(req.otherUser);//add loginSources to permamnently banned list

    // ban users with the same device id
    if (req.otherUser.deviceId) {
      const sameDeviceUsers = await User.find({
        deviceId: req.otherUser.deviceId,
        shadowBanned: false,
      });
      for (const sameDeviceUser of sameDeviceUsers) {
        await shadowBan(sameDeviceUser, undefined, 'deviceId');
      }
    }

    return res.json({});
  }));

  router.put('/bannedNotes', checkPermissions(['support']), asyncHandler(findOtherUser), asyncHandler(async (req, res, next) => {

    req.otherUser.bannedNotes = req.body.bannedNotes;
    req.otherUser.banHistory.push({
      action: 'notes',
      by: req.uid,
      date: Date.now(),
      notes: req.body.bannedNotes,
    });
    await req.otherUser.save();

    return res.json({});
  }));

  router.put('/unban', checkPermissions('manager'), asyncHandler(async (req, res, next) => {
    const { user: userId, notes } = req.body;
    let user = await User.findById(userId);

    if (user) {
      await unban(user, req.uid, notes);

      if (user.signupCountry == 'Philippines') {
        const searched = await AdminSearch.findOne({
          admin: req.user._id,
          searchedUser: user._id,
        });
        if (searched) {
          await emailLib.sendAlertForAdminAction(`Admin ${req.user._id} (${req.user.email}) unbanned an account with Philippines timezone: ${user._id}. Current time: ${(new Date()).toISOString()}, most recent search: ${searched.mostRecentSearch?.toISOString()}, num searches: ${searched.numSearches}`);
        }
      }
    } else {
      user = await unbanDeletedUser(userId, req.uid, notes);
    }
    res.json({});

    // Mass unban all users on the same device
    if (user) {
      unBanAllUsersOnSameDevice(user, req.uid, notes);
    }
  }));

  router.get('/banAppeal', checkPermissions(['manager','reviewBanAppeal']), asyncHandler(async (req, res, next) => {

    const category1BanReasons = [
      'Impersonation',
      'impersonation',
      'Inappropriate Messages',
      'inappropriate messages',
      'Inappropriate Profile',
      'inappropriate profile',
      'Prostitution and Trafficking',
      'prostitution',
      'Underage',
      'underage',
      'Auto-ban: underage',
      'infringing text found in name',
      'banned face found in liveness verification picture',
      'banned face found in liveness verification picture (new logic)',
    ];

    const query = {
      decision: { $exists: false },
    };

    // disabled due to trial for allowing reviewBanAppeal admins to view all ban appeals
    /*
    if (req.user.adminPermissions.reviewBanAppeal) {
      query.bannedReasons = { $in: category1BanReasons };
    } else {
      if (req.query.queue == 1) {
        query.bannedReasons = { $in: category1BanReasons };
      }
      if (req.query.queue == 0) {
        query.bannedReasons = { $nin: category1BanReasons };
      }
    }
    */

    if (req.query.queue == 1) {
      query.bannedReasons = { $in: category1BanReasons };
    }
    if (req.query.queue == 0) {
      query.bannedReasons = { $nin: category1BanReasons };
    }

    const appeals = await BanAppeal.find(query, 'createdAt user comment').sort('createdAt').lean()

    return res.json({ appeals });
  }));

  router.put('/banAppeal/decision', checkPermissions(['manager','reviewBanAppeal']), asyncHandler(async (req, res, next) => {
    const { appealId, decision, notes } = req.body;

    if (!appealId || !['approved', 'rejected'].includes(decision)) {
      return next(invalidInputError());
    }

    const appeal = await BanAppeal.findById(appealId);
    if (!appeal) {
      return next(notFoundError('Appeal not found'));
    }
    if (appeal.decision) {
      return conflictError('Already reviewed');
    }

    appeal.reviewedBy = req.uid;
    appeal.reviewedAt = Date.now();
    appeal.decision = decision;
    appeal.notes = notes;
    await appeal.save();

    const user = await User.findById(appeal.user);
    if (!user) {
      return next(notFoundError('User not found'));
    }

    if (decision == 'rejected') {
      if (user.banNotice) {
        user.banNotice.appealStatus = 'rejected';
        user.banHistory.push({
          action: 'appealRejected',
          by: req.uid,
          date: Date.now(),
          notes,
        });
        await user.save();
      }
    }
    else if (decision == 'approved') {
      user.banNotice = undefined;
      user.banHistory.push({
        action: 'appealApproved',
        by: req.uid,
        date: Date.now(),
        notes,
      });
      await user.save();
      await unban(user, req.uid, `${notes} (appeal approved)`);
      unBanAllUsersOnSameDevice(user, req.uid, `${notes} (appeal approved)`);
    }

    return res.json({});
  }));

  router.get('/profileTempBanAppeal', checkPermissions(['manager','reviewBanAppeal']), asyncHandler(async (req, res, next) => {
    const appeals = await ProfileTempBanAppeal.find({
      decision: { $exists: false },
    }, 'createdAt user comment').sort('createdAt').lean()

    return res.json({ appeals });
  }));

  router.put('/profileTempBanAppeal/decision', checkPermissions(['manager','reviewBanAppeal']), asyncHandler(async (req, res, next) => {
    const { appealId, decision, notes } = req.body;

    if (!appealId || !['approved', 'rejected'].includes(decision)) {
      return next(invalidInputError());
    }

    const appeal = await ProfileTempBanAppeal.findById(appealId);
    if (!appeal) {
      return next(notFoundError('Appeal not found'));
    }
    if (appeal.decision) {
      return conflictError('Already reviewed');
    }

    appeal.reviewedBy = req.uid;
    appeal.reviewedAt = Date.now();
    appeal.decision = decision;
    appeal.notes = notes;
    await appeal.save();

    const user = await User.findById(appeal.user);
    if (!user) {
      return next(notFoundError('User not found'));
    }

    if (decision == 'rejected') {
      user.accountRestrictions.profileTempBan.appealStatus = 'rejected';
      user.banHistory.push({
        action: 'profileTempBanAppealRejected',
        by: req.uid,
        date: Date.now(),
        notes,
      });
      await user.save();
    }
    else if (decision == 'approved') {
      if(user.profileTempBanInfringingText && user.profileTempBanInfringingText.length){
        if(!user.appealedProfileTempBanInfringingText) user.appealedProfileTempBanInfringingText = []
        user.appealedProfileTempBanInfringingText = [...user.appealedProfileTempBanInfringingText, ...user.profileTempBanInfringingText ]
      }
      if(user.profileTempBanInfringingPictures && user.profileTempBanInfringingPictures.length){
        if(!user.appealedProfileTempBanInfringingPictures) user.appealedProfileTempBanInfringingPictures = []
        user.appealedProfileTempBanInfringingPictures = [...user.appealedProfileTempBanInfringingPictures, ...user.profileTempBanInfringingPictures ]
      }
      user.banHistory.push({
        action: 'profileTempBanAppealApproved',
        by: req.uid,
        date: Date.now(),
        notes,
      });
      await user.save();
      await reportLib.undoProfileTempBan(user);
    }

    return res.json({});
  }));

  router.get('/tempBanAppeal', checkPermissions(['manager','reviewBanAppeal']), asyncHandler(async (req, res, next) => {
    const appeals = await TempBanAppeal.find({
      decision: null,
      tempBanEndAt: { $gt: Date.now() },
    }, 'createdAt user comment').sort('createdAt').lean()

    return res.json({ appeals });
  }));

  router.put('/tempBanAppeal/decision', checkPermissions(['manager','reviewBanAppeal']), asyncHandler(async (req, res, next) => {
    const { appealId, decision, notes } = req.body;

    if (!appealId || !['approved', 'rejected'].includes(decision)) {
      return next(invalidInputError());
    }

    const appeal = await TempBanAppeal.findById(appealId);
    if (!appeal) {
      return next(notFoundError('Appeal not found'));
    }
    if (appeal.decision) {
      return conflictError('Already reviewed');
    }

    appeal.reviewedBy = req.uid;
    appeal.reviewedAt = Date.now();
    appeal.decision = decision;
    appeal.notes = notes;
    await appeal.save();

    const user = await User.findById(appeal.user);
    if (!user) {
      return next(notFoundError('User not found'));
    }

    if (decision == 'rejected') {
      user.accountRestrictions.tempBan.appealStatus = 'rejected';
      user.banHistory.push({
        action: 'tempBanAppealRejected',
        by: req.uid,
        date: Date.now(),
        notes,
      });
      await user.save();
    }
    else if (decision == 'approved') {
      user.banHistory.push({
        action: 'tempBanAppealApproved',
        by: req.uid,
        date: Date.now(),
        notes,
      });
      await user.save();
      await reportLib.undoTempBan(user, notes, req.user);
    }

    return res.json({});
  }));

  router.put('/banComment', checkPermissions(['support', 'translator']), asyncHandler(async (req, res, next) => {
    const { commentId, bannedReasons } = req.body;

    if (bannedReasons && (!Array.isArray(bannedReasons) || bannedReasons.some(reason => typeof reason !== 'string' || reason.length > 500))) {
      return next(invalidInputError('Invalid banned reasons'));
    }

    if (req.user.adminPermissions.translator) {
      const post = await Comment.findById(commentId);
      if (!post || post.language != req.user.adminPermissions.translator) {
        return next(forbiddenError());
      }
    }

    await banPost(Comment, commentId, req.user._id, bannedReasons);
    return res.json({});
  }));

  router.put('/dismissComment', checkPermissions(['support', 'translator']), asyncHandler(async (req, res, next) => {
    if (req.user.adminPermissions.translator) {
      const post = await Comment.findById(req.body.commentId);
      if (!post || post.language != req.user.adminPermissions.translator) {
        return next(forbiddenError());
      }
    }
    await dismissPost(Comment, req.body.commentId);
    res.json({});
  }));
  router.get('/reportedComments', checkPermissions(['support', 'translator']), asyncHandler(async (req, res, next) => {
    const matchQuery = { flagged: true };
    if (req.user.adminPermissions.translator) {
      matchQuery.language = req.user.adminPermissions.translator;
    }
    const comments = await getPosts(
      req.user,
      null,
      null,
      matchQuery,
      null,
      null,
      Comment,
      formatComment,
      true, // lookupQuestion for questionUrl
      true,
    );
    res.json({
      comments,
    });
  }));

  router.put('/banQuestion', checkPermissions(['support', 'translator']), asyncHandler(async (req, res, next) => {
    const { questionId, bannedReasons } = req.body;

    if (bannedReasons && (!Array.isArray(bannedReasons) || bannedReasons.some(reason => typeof reason !== 'string' || reason.length > 500))) {
      return next(invalidInputError('Invalid banned reasons'));
    }

    if (req.user.adminPermissions.translator) {
      const post = await Question.findById(questionId);
      if (!post || post.language != req.user.adminPermissions.translator) {
        return next(forbiddenError());
      }
    }

    await banPost(Question, questionId, req.user._id, bannedReasons);
    return res.json({});
  }));

  router.put('/dismissQuestion', checkPermissions(['support', 'translator']), asyncHandler(async (req, res, next) => {
    if (req.user.adminPermissions.translator) {
      const post = await Question.findById(req.body.questionId);
      if (!post || post.language != req.user.adminPermissions.translator) {
        return next(forbiddenError());
      }
    }
    await dismissPost(Question, req.body.questionId);
    res.json({});
  }));
  router.get('/reportedQuestions', checkPermissions(['support', 'translator']), asyncHandler(async (req, res, next) => {
    const matchQuery = { flagged: true };
    if (req.user.adminPermissions.translator) {
      matchQuery.language = req.user.adminPermissions.translator;
    }
    const posts = await getPosts(
      req.user,
      null,
      null,
      matchQuery,
      null,
      null,
      Question,
      formatQuestion,
      null,
      true,
    );
    res.json({
      questions: posts,
    });
  }));

  router.get('/reports', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const reports = await Report
      .find({ status: 'needs review' })
      .sort('-updatedAt')
      .limit(100)

    res.json({ reports });
  }));

  router.get('/recentlyVerifiedReports', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const reports = await Report.find(
      { status: 'verified' },
    )
      .sort('-updatedAt')
      .limit(100);

    res.json({ reports });
  }));

  router.get('/recentlyDismissedReports', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const reports = await Report.find(
      { status: 'dismissed' },
    )
      .sort('-updatedAt')
      .limit(100);

    res.json({ reports });
  }));

  router.get('/verifyProfile', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const users = await User.find(
      { 'verification.status': { $in: ['pending', 'reverifying'] } },
      projections.userProfileAdminFields.join(' '),
    )
      .sort('verification.updatedAt')
      .lean();
    users.forEach(user => normalizeUserFields(user));

    res.json({ users });
  }));

  router.get('/verifyProfile/pending', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const users = await User.aggregate([
      { $match: { 'verification.status': 'pending' } },
      { $sort: { 'verification.updatedAt': 1 } },
      { $project: projections.userProfileAdminFieldsObj },
      { $lookup: {
          from: 'reports',
          localField: '_id',
          foreignField: 'reportedUser',
          as: 'reports',
        },
      },
    ])
    users.forEach(user => normalizeUserFields(user));

    res.json({ users });
  }));

  router.get('/verifyProfile/reverifying', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const users = await User.find(
      { 'verification.status': 'reverifying' },
      projections.userProfileAdminFields.join(' '),
    )
      .sort('verification.updatedAt')
      .lean();
    users.forEach(user => normalizeUserFields(user));

    res.json({ users });
  }));

  router.get('/verifyProfile/pendingQueued', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    let users = await getProfilesToVerify('pending');

    async function getReports(user) {
      user.reports = await Report.find({ reportedUser: user._id });
    }
    await Promise.all(users.map((user) => getReports(user)));

    res.json({ users });
  }));

  router.get('/verifyProfile/reverifyingQueued', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    let users = await getProfilesToVerify('reverifying');

    res.json({ users });
  }));

  router.get('/recentlyVerifiedProfiles', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const users = await User.find(
      { 'verification.verifiedDate': { $lt: Date.now() } },
      projections.userProfileAdminFields.join(' '),
    )
      .sort('-verification.verifiedDate')
      .lean()
      .limit(100);
    users.forEach(user => normalizeUserFields(user));

    res.json({ users });
  }));

  router.patch('/verifyProfile', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const user = await User.findById(req.body.user);
    if (!user) {
      return next(invalidInputError());
    }

    const isFirstVerification = user.verification.status == 'pending';
    if (user.verification.status == 'pending') {
      const waitTime = moment().diff(user.verification.updatedAt, 'seconds');
      {
        const params = {
          MetricData: [
            {
              MetricName: 'PhotoVerification',
              Value: waitTime,
              Unit: 'Seconds',
            },
          ],
          Namespace: `SupportTeamMetrics_${process.env.NODE_ENV}`,
        };
        await cloudwatch.putMetricData(params).promise();
      }
      {
        const params = {
          MetricData: [
            {
              MetricName: 'PhotoVerificationPerAdmin',
              Value: waitTime,
              Unit: 'Seconds',
              Dimensions: [
                {
                  Name: 'adminId',
                  Value: req.uid,
                }
              ],
            },
          ],
          Namespace: `SupportTeamMetrics_${process.env.NODE_ENV}`,
        };
        await cloudwatch.putMetricData(params).promise();
      }
    }
    else if (user.verification.status == 'reverifying') {
      const waitTime = moment().diff(user.verification.updatedAt, 'seconds');
      const params = {
        MetricData: [
          {
            MetricName: 'PhotoReverification',
            Value: waitTime,
            Unit: 'Seconds',
          },
        ],
        Namespace: `SupportTeamMetrics_${process.env.NODE_ENV}`,
      };
      await cloudwatch.putMetricData(params).promise();
    }

    await handleVerificationDecision(user, req.body.verified, req.body.rejectionReason, req.uid);

    user.verification.manualReview = undefined;
    await user.save();

    if (req.body.user == req.user._id) {
      await emailLib.sendAlertForAdminAction(`Admin ${req.user._id} (${req.user.email}) attempted to verify own account. ${JSON.stringify(req.body)}`);
    }

    if (req.body.verified && user.signupCountry == 'Philippines') {
      const searched = await AdminSearch.findOne({
        admin: req.user._id,
        searchedUser: user._id,
      });
      if (searched) {
        await emailLib.sendAlertForAdminAction(`Admin ${req.user._id} (${req.user.email}) verified an account with Philippines timezone: ${user._id}. Current time: ${(new Date()).toISOString()}, most recent search: ${searched.mostRecentSearch?.toISOString()}, num searches: ${searched.numSearches}`);
      }
    }

    res.json({});
  }));

  router.get('/user', checkPermissions(['support', 'setConfig']), asyncHandler(async (req, res, next) => {
    if (!req.query.id) {
      return next(invalidInputError());
    }

    let deletedAccount;
    let user = await User.findOne({
      $or: [
        { _id: req.query.id },
        { handle: req.query.id },
        { email: req.query.id },
        { phoneNumber: req.query.id.startsWith('+') ? req.query.id : '+' + req.query.id.trim() },
      ],
    }).lean();
    if (!user) {
      let bannedData = await BannedUser.findOne({ user: req.query.id }).sort('-userData.bannedDate');
      if (bannedData) {
        user = bannedData.userData;
        deletedAccount = true;
      }
    }
    if (!user) {
      return next(notFoundError());
    }

    await AdminSearch.updateOne(
      {
        admin: req.user._id,
        searchedUser: user._id,
      },
      {
        $set: { mostRecentSearch: Date.now() },
        $inc: { numSearches: 1 },
      },
      { upsert: true },
    );

    const formattedUser = chatLib.formatProfile(user, req.user);
    const reports = await Report.find({ reportedUser: user._id });
    const userMetadata = await UserMetadata.findOne({ user: user._id });
    if (userMetadata) {
      user.coins = userMetadata.coins;
    }

    let sameDeviceId = [];
    if (user.deviceId) {
      sameDeviceId = await User.distinct('_id', {
        _id: { $ne: req.query.id },
        deviceId: user.deviceId,
      });

      // check for possibly deleted banned accounts
      let additionalAccounts = await BannedUser.distinct('user', {
        'userData.deviceId': user.deviceId,
        user: { $nin: [req.query.id].concat(sameDeviceId) },
      });
      sameDeviceId = sameDeviceId.concat(additionalAccounts);
    }

    let query = [ { user: req.query.id } ];
    if (user.phoneNumber) {
      query.push({ 'userData.phoneNumber': user.phoneNumber });
    }
    if (user.email) {
      query.push({ 'userData.email': user.email });
    }
    let bannedData = await BannedUser.find({$or: query});
    let bannedProfileData;
    if (bannedData) {
      bannedProfileData = bannedData.map(function(x) {
        return {
          date: x.userData.bannedDate,
          profile: chatLib.formatProfile(x.userData, req.user),
        }
      });
    }

    const verification = user.verification;
    if (!verification.verifiedDate && verification.updatedAt) {
      verification.verifiedDate = verification.updatedAt;
    }

    if (user.banHistory?.length) {
      const markers = {
        previouslyBannedUser: /previously banned user:\s*(.+)/i,
      };

      user.banHistory = user.banHistory.map(entry => {
        let updatedEntry = { ...entry };

        for (const [key, regex] of Object.entries(markers)) {
          const match = entry.notes?.match(regex);
          if (match) {
            updatedEntry[key] = match[1].trim();
          }
        }

        return updatedEntry;
      });
    }

    res.json({
      user: formattedUser,
      metadata: user,
      verification,
      reports,
      accountsWithSameDeviceId: sameDeviceId,
      bannedSources: await BannedSource.getBannedSources(user),
      bannedProfileData,
      deletedAccount,
    });
  }));

  router.get('/userByProfileDetails', checkPermissions('manager'), asyncHandler(async (req, res, next) => {
    const { firstName, age, gender, mbti, horoscope, city, work, education } = req.query;

    let filters = [];

    if (firstName) {
      filters = filters.concat([
        {
          equals: {
            path: "firstName",
            value: firstName,
          }
        },
      ]);
    }
    if (age) {
      filters = filters.concat([
        {
          equals: {
            path: "age",
            value: parseInt(age),
          }
        },
      ]);
    }
    if (gender) {
      filters = filters.concat([
        {
          equals: {
            path: "gender",
            value: gender,
          }
        },
      ]);
    }
    if (mbti) {
      filters = filters.concat([
        {
          equals: {
            path: "personality.mbti",
            value: mbti,
          }
        },
      ]);
    }
    if (horoscope) {
      filters = filters.concat([
        {
          equals: {
            path: "horoscope",
            value: horoscope,
          }
        },
      ]);
    }
    if (city) {
      filters = filters.concat([
        {
          equals: {
            path: "city",
            value: city,
          }
        },
      ]);
    }
    if (work) {
      filters = filters.concat([
        {
          equals: {
            path: "work",
            value: work,
          }
        },
      ]);
    }
    if (education) {
      filters = filters.concat([
        {
          equals: {
            path: "education",
            value: education,
          }
        },
      ]);
    }

    if (filters.length == 0) {
      return res.json({ users: [] });
    }

    let users = await User.aggregate([
      {
        $search: {
          index: "users",
          compound: {
            must: filters,
          },
        }
      },
      {
        $limit: 10
      },
    ]);

    users = users.map(user => chatLib.formatProfile(user, req.user));
    return res.json({ users });
  }));

  router.get('/messages', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    if (!req.query.before) {
      const report = await Report.findOne({
        reportedUser: req.query.reportedUser,
        reportedBy: req.query.reportedBy,
      });
      if (report && report.messages && report.messages.length > 0) {
        return res.json(report.messages);
      }
    }

    // include deleted chat and messages
    const chat = await Chat.findDirectChat(req.query.reportedUser, req.query.reportedBy, null, true);
    if (!chat) {
      return res.json([]);
    }
    const messages = await getMessages(chat._id, req.query.before, null, true);
    res.json(messages);
  }));

  router.get('/messagesSentByUser', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const chatIds = await Chat
        .find({ users: req.query.user, pendingUser: null }, { _id: 1 })
        .sort('-lastMessageTime')
        .limit(50);
    let messages = await Message
        .find({
          chat: { $in: chatIds },
          sender: req.query.user,
        })
        .sort('-createdAt')
        .limit(100);
    messages = messages.map(formatMessage);
    res.json(messages);
  }));

  router.get('/deletedChats', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const user = await User.findById(req.query.user);
    if (!user) {
      return next(notFoundError());
    }

    const query = {
      users: user._id,
      pendingUser: null,
      messaged: true,
      deletedAt: { $ne: null },
    };

    let chats = await Chat
      .find(query)
      .sort('-lastMessageTime')
      .limit(100)
      .populate('users', projections.fullProfileFieldsStr)
      .populate('lastMessage')
      .lean();
    chats = chats.filter(chat => chat.users.length >= 2);

    res.json({
      deletedChats: chatLib.formatChats(chats, user),
    });
  }));

  router.get('/deletedChatMessages', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    let messages = await Message
        .find({
          chat: req.query.chatId,
        })
        .sort('-createdAt')
        .limit(100);
    messages = messages.map(formatMessage);
    res.json({
      messages,
    });
  }));

  router.get('/usersWhoBlocked', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    let docs = await Block
      .find({ to: req.query.user })
      .sort('-createdAt')
      .limit(10)
      .populate('from')
    let users = docs.map(x => chatLib.formatProfile(x.from, req.user));
    res.json({
      users,
    });
  }));

  router.get('/reportsForUser', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const reports = await Report.find({ reportedUser: req.query.user });
    res.json({ reports });
  }));

  router.get('/user/coinTransactions', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    let strBeforeDate = req.query.before;
    const query = {
      user: req.query.user,
    };
    if (strBeforeDate) {
      const dtBeforeDate = moment(strBeforeDate);
      query.createdAt = { $lt: dtBeforeDate.toDate() };
    }
    const transactions = await CoinTransaction.find(query).sort('-createdAt').limit(pageSize);
    res.json({ transactions });
  }));

  router.get('/user/boostTransactions', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    let strBeforeDate = req.query.before;
    const query = {
      user: req.query.user,
    };
    if (strBeforeDate) {
      const dtBeforeDate = moment(strBeforeDate);
      query.createdAt = { $lt: dtBeforeDate.toDate() };
    }
    const transactions = await BoostTransaction.find(query).sort('-createdAt').limit(pageSize);
    res.json({ transactions });
  }));

  router.get('/user/superLikeTransactions', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    let strBeforeDate = req.query.before;
    const query = {
      user: req.query.user,
    };
    if (strBeforeDate) {
      const dtBeforeDate = moment(strBeforeDate);
      query.createdAt = { $lt: dtBeforeDate.toDate() };
    }
    const transactions = await SuperLikeTransaction.find(query).sort('-createdAt').limit(pageSize);
    res.json({ transactions });
  }));

  router.get('/user/premiumPurchases', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const purchases = await PurchaseReceipt.find({ user: req.query.user }, 'productId purchaseDate service expirationDate revokedAt cancelledAt isRefund transactionId subscriptionId renewalNumber').sort('-purchaseDate');
    res.json({ purchases });
  }));

  router.get('/user/coinPurchases', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const purchases = await CoinPurchaseReceipt.find({ user: req.query.user }, 'productId purchaseDate').sort('-purchaseDate');
    res.json({ purchases });
  }));

  router.get('/user/superLikePurchases', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const purchases = await SuperLikePurchaseReceipt.find({ user: req.query.user }, 'productId purchaseDate').sort('-purchaseDate');
    res.json({ purchases });
  }));

  router.get('/user/neuronPurchases', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const purchases = await NeuronPurchaseReceipt.find({ user: req.query.user }, 'productId purchaseDate').sort('-purchaseDate');
    res.json({ purchases });
  }));

  router.get('/user/boostPurchases', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const purchases = await BoostPurchaseReceipt.find({ user: req.query.user }, 'productId purchaseDate').sort('-purchaseDate');
    res.json({ purchases });
  }));

  router.get('/user/postReports', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const reports = await PostReport
      .find({ reportedUser: req.query.user }, '-_id -prompt')
      .sort('-createdAt')
      .populate('reportedQuestion', 'createdAt title text image images altText audio audioTranscription gif poll interestName')
      .populate('reportedComment', 'createdAt text image audio audioTranscription gif')
    res.json({ reports });
  }));

  router.get('/user/messageReports', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const reports = await MessageReport.find({ user: req.query.user }).sort('-createdAt');
    res.json({ reports });
  }));

  router.put('/dismissReports', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    // publish metrics to cloudwatch
    const reports = await Report.find({ reportedUser: req.body.user, status: 'needs review' });
    for (const report of reports) {
      const waitTime = moment().diff(report.createdAt, 'seconds');
      const params = {
        MetricData: [
          {
            MetricName: 'ProfileReports',
            Value: waitTime,
            Unit: 'Seconds',
          },
        ],
        Namespace: `SupportTeamMetrics_${process.env.NODE_ENV}`,
      };
      await cloudwatch.putMetricData(params).promise();
    }

    await Report.dismissReports({ reportedUser: req.body.user }, req.uid);
    res.json({});
  }));

  router.put('/userBirthday', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const user = await User.findById(req.body.user);
    if (!user) {
      return next(notFoundError());
    }
    const birthday = new Date(Date.UTC(
      req.body.year,
      req.body.month - 1, // Javascript months are 0 indexed
      req.body.day,
    ));
    user.setBirthday(birthday, user.timezone);
    await user.save();
    res.json({});
  }));

  router.put('/userLocation', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const user = await User.findById(req.body.user);
    if (!user) {
      return next(notFoundError());
    }

    user.disableLocationUpdates = true;
    if (req.body.city) {
      user.city = req.body.city;
      user.actualCity = req.body.city;
    }
    if (req.body.state) {
      let { state } = req.body;
      if (state.length == 2) {
        const found = usStatesLookup.getStateNameByStateCode(state);
        if (found) {
          state = found;
        }
      }
      user.state = state;
      user.actualState = state;
    }
    if (req.body.country) {
      let found = countryLookup.byCountry(req.body.country);
      if (!found && req.body.country.length == 2) {
        found = countryLookup.byIso(req.body.country);
      }
      if (!found) {
        return next(invalidInputError('Invalid country'));
      }
      user.country = found.country;
      user.actualCountry = found.country;
      user.countryCode = found.iso2;
      user.actualCountryCode = found.iso2;
    }
    await user.save();
    res.json({
      location: locationLib.getFormattedLocationFromUser(user),
    });
  }));

  router.delete('/userLocation', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const user = await User.findById(req.body.user);
    if (!user) {
      return next(notFoundError());
    }

    user.disableLocationUpdates = undefined;
    await user.save();
    res.json({
      location: locationLib.getFormattedLocationFromUser(user),
    });
  }));

  router.put('/userPremiumExpiration', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const user = await User.findById(req.body.user);
    if (!user) {
      return next(notFoundError());
    }
    const date = new Date(Date.UTC(
      req.body.year,
      req.body.month - 1, // Javascript months are 0 indexed
      req.body.day,
    ));
    user.premiumExpiration = date;
    await user.activateInfinitySuperLikes();
    await user.save();

    await emailLib.sendAlertForAdminAction(`Admin ${req.user._id} (${req.user.email}) modified premium expiration for user. ${JSON.stringify(req.body)}`);

    res.json({});
  }));

  router.patch('/incrementCoins', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    await coinsLib.updateCoins(
      { user: req.body.user },
      { $inc: { coins: req.body.coins } },
      'admin incremented coins',
    );

    await emailLib.sendAlertForAdminAction(`Admin ${req.user._id} (${req.user.email}) gave coins to user. ${JSON.stringify(req.body)}`);

    res.json({});
  }));

  router.patch('/incrementSuperLikes', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    await User.updateOne(
      { _id: req.body.user },
      { $inc: { numSuperLikes: req.body.numSuperLikes } },
    );
    const user = await User.findById(req.body.user);
    if (user) {
      await SuperLikeTransaction.create({
        user: user._id,
        freeSuperLoveTransactionAmount: 0,
        freeSuperLoveNewBalance: user.numSuperLikesFree,
        paidSuperLoveTransactionAmount: req.body.numSuperLikes,
        paidSuperLoveNewBalance: user.numSuperLikes,
        description: 'admin incremented paid super love',
      });
    }

    await emailLib.sendAlertForAdminAction(`Admin ${req.user._id} (${req.user.email}) gave super love to user. ${JSON.stringify(req.body)}`);

    res.json({});
  }));

  router.patch('/incrementBoosts', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    console.log('numboost: ', req.body.numBoosts)
    await User.updateOne(
      { _id: req.body.user },
      { $inc: { numBoosts: req.body.numBoosts } },
    );
    const user = await User.findById(req.body.user);
    if (user) {
      await BoostTransaction.create({
        user: user._id,
        transactionAmount: req.body.numBoosts,
        newBalance: user.numBoosts,
        description: 'admin incremented boosts',
      });
    }

    await emailLib.sendAlertForAdminAction(`Admin ${req.user._id} (${req.user.email}) gave boosts to user. ${JSON.stringify(req.body)}`);

    res.json({});
  }));

  router.patch('/incrementNeurons', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    await User.updateOne(
      { _id: req.body.user },
      { $inc: { numBooAINeurons: req.body.numBooAINeurons } },
    );
    res.json({});
  }));

  router.get('/pendingInterests', checkPermissions('approveInterest'), asyncHandler(async (req, res, next) => {
    const pendingInterests = await Interest.find({ status: 'pending' });
    res.json({
      interests: pendingInterests,
    });
  }));

  router.put('/approveInterest', checkPermissions('approveInterest'), asyncHandler(async (req, res, next) => {
    const interest = await Interest
      .findOne({ name: req.body.name, status: 'pending' })
      .populate('createdBy');
    if (!interest) {
      return next(notFoundError());
    }
    interest.pending = undefined;
    interest.status = undefined;
    interest.numFollowers += 1;
    interest.numFollowersSortIndex += 1;
    await interest.save();

    // publish metrics to cloudwatch
    const waitTime = moment().diff(interest.createdAt, 'seconds');
    const params = {
      MetricData: [
        {
          MetricName: 'InterestApproval',
          Value: waitTime,
          Unit: 'Seconds',
        },
      ],
      Namespace: `SupportTeamMetrics_${process.env.NODE_ENV}`,
    };
    await cloudwatch.putMetricData(params).promise();

    const user = interest.createdBy;
    if (user) {
      sendNotification(
        user,
        null,
        '',
        translate('Your interest #%s has been approved!', user.locale, interest.name),
        null,
        null,
        'general',
        'interest-approved',
      );

      if (!user.interestNames) {
        user.interestNames = [];
      }
      user.interestNames.push(interest.name);
      await InterestCountryCount.incrementCount(interest.name, interest.libCategory || interest.category || null, user.ipData?.country, user.locale)
      await user.save();
    }

    await Question.updateMany(
      { keywords: interest.name },
      { $addToSet: { linkedKeywords: interest.name } },
    );
    await Comment.updateMany(
      { keywords: interest.name },
      { $addToSet: { linkedKeywords: interest.name } },
    );

    res.json({});
  }));

  router.put('/rejectInterest', checkPermissions('approveInterest'), asyncHandler(async (req, res, next) => {
    const interest = await Interest
      .findOne({ name: req.body.name, status: 'pending' })
      .populate('createdBy');
    if (!interest) {
      return next(notFoundError());
    }

    // publish metrics to cloudwatch
    const waitTime = moment().diff(interest.createdAt, 'seconds');
    const params = {
      MetricData: [
        {
          MetricName: 'InterestApproval',
          Value: waitTime,
          Unit: 'Seconds',
        },
      ],
      Namespace: `SupportTeamMetrics_${process.env.NODE_ENV}`,
    };
    await cloudwatch.putMetricData(params).promise();

    interest.pending = undefined;
    interest.status = 'rejected';
    await interest.save();

    const user = interest.createdBy;
    if (user) {
      sendNotification(
        user,
        null,
        '',
        translate('Your interest #%s was not approved.', user.locale, interest.name),
        null,
        null,
        'negative',
        'interest-rejected',
      );
    }

    res.json({});
  }));

  router.put('/userNumber', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const { phoneNumber } = req.body;
    if (phoneNumber !== null && typeof phoneNumber !== 'string') {
      throw invalidInputError();
    }

    const userId = req.body.user;

    const toUpdate = await User.findOne({ _id: userId }, { _id: 1, phoneNumber: 1 });

    if (!toUpdate) {
      throw notFoundError('User not found!');
    }

    if (phoneNumber) {
      if (await User.findOne({ _id: { $ne: userId }, phoneNumber }, { _id: 1 })) {
        throw conflictError('User already exists with same phone number!');
      }
    }

    const newNumber = (await updatePhoneNumber(userId, phoneNumber)).phoneNumber || null;
    const oldNumber = toUpdate.phoneNumber;

    toUpdate.phoneNumber = newNumber;
    await toUpdate.save();

    await (new PhoneUpdateLogs({
      adminId: req.user._id,
      userId,
      old_num: oldNumber,
      new_num: newNumber,
    }).save());

    res.json({});
  }));

  router.put('/userEmail', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const { email } = req.body;
    if (typeof email !== 'string') {
      throw invalidInputError();
    }

    const userId = req.body.user;

    const toUpdate = await User.findOne({ _id: userId }, { _id: 1, email: 1 });

    if (!toUpdate) {
      throw notFoundError('User not found!');
    }

    if (await User.findOne({ _id: { $ne: userId }, email }, { _id: 1 })) {
      throw conflictError('User already exists with same email!');
    }

    await updateEmail(userId, email);
    const oldEmail = toUpdate.email;

    toUpdate.email = email;
    await toUpdate.save();

    await (new PhoneUpdateLogs({
      adminId: req.user._id,
      userId,
      old_email: oldEmail,
      new_email: email,
    }).save());

    res.json({});
  }));

  router.delete('/user', checkPermissions(['support', 'manager']), asyncHandler(async (req, res, next) => {
    const query = {
      _id: req.body.userId,
    };
    if (!req.user.adminPermissions.all) {
      // only "all" can delete accounts even if the user has not requested account deletion
      query.deletionRequestDate = { $lte: new Date() }
    }
    const user = await User.findOne(query);
    if (!user) {
      throw notFoundError();
    }

    console.log(`admin: ${req.user._id} initiated deletion of user:${user._id}`);
    await userLib.deleteAccount(user);
    res.json({});
  }));

  router.put('/tempBan', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const banId = req.body.banUser;
    const { reason, infringingPost, infringingComment, infringingChatWithUser } = req.body;

    if (!banId || (typeof reason !== 'string') || (reason.length === 0)) {
      throw invalidInputError();
    }

    const user = await User.findOne({ _id: banId });
    if (!user) {
      throw notFoundError('user not found');
    }
    if (!user.versionAtLeast('1.11.45')) {
      throw notFoundError('user is on old version and cannot be temp banned');
    }

    let evidence;
    if (infringingChatWithUser) {
      if (typeof infringingChatWithUser !== 'string') {
        throw invalidInputError('Invalid user id');
      }
      const messages = await reportLib.getMessagesBetweenUsers(user._id, infringingChatWithUser);
      if (!messages || !messages.length) {
        throw invalidInputError('Chat messages not found');
      }
      evidence = { messages };
    }
    if (infringingPost) {
      if (!mongoose.isValidObjectId(infringingPost)) {
        throw invalidInputError('Invalid post id');
      }
      const post = await Question.findOne({ _id: infringingPost, createdBy: user._id }, '_id createdAt title text image images audio gif poll').lean();
      if (!post) {
        throw invalidInputError('Post not found');
      }
      evidence = { post };
    }
    if (infringingComment) {
      if (!mongoose.isValidObjectId(infringingComment)) {
        throw invalidInputError('Invalid comment id');
      }
      const comment = await Comment.findOne({ _id: infringingComment, createdBy: user._id }, '_id createdAt text image audio gif').lean();
      if (!comment) {
        throw invalidInputError('Comment not found');
      }
      evidence = { comment };
    }

    await tempBan(user, reason, req.uid, evidence);

    res.json({});
  }));

  router.put('/undoTempBan', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const banId = req.body.user;
    const { notes } = req.body;

    if (!banId) {
      throw invalidInputError();
    }

    const user = await User.findOne({ _id: banId });
    if (!user) {
      throw notFoundError('user not found');
    }

    await reportLib.undoTempBan(user, notes, req.user);

    res.json({});
  }));

  router.get('/questionCandidates', checkPermissions(['approveQod', 'translator', 'translatorManager']), asyncHandler(async (req, res, next) => {
    const { lastSent } = req.query;
    const { status } = req.query;
    const { language } = req.query;
    const { order } = req.query;
    let count = Number(req.query.count || CANDIDATE_MAX_COUNT);
    const matchQuery = {
      status: 'pending',
    };

    if (status) {
      if (['pending', 'approved', 'rejected'].includes(status)) {
        matchQuery.status = status;
      } else {
        throw invalidInputError();
      }
    }

    if (req.user.adminPermissions.translator) {
      matchQuery.language = req.user.adminPermissions.translator;
    } else if (language) {
      matchQuery.language = language;
    }

    if (Number.isNaN(count)) {
      throw invalidInputError();
    } else if (count) {
      if (Number.isInteger(count)) {
        if (count < 0 || count > CANDIDATE_MAX_COUNT) {
          count = CANDIDATE_MAX_COUNT;
        }
      } else {
        throw invalidInputError();
      }
    }

    let reverse = true;
    if (order) {
      if (['asc', 'desc'].includes(order)) {
        reverse = (order == 'desc');
      } else { throw invalidInputError(); }
    }

    if (lastSent) {
      if (ObjectId.isValid(lastSent)) {
        matchQuery._id = { [reverse ? '$lt' : '$gt']: lastSent };
      } else { throw invalidInputError(); }
    }

    const data = await QuestionCandidate.find(matchQuery).sort({ _id: (reverse) ? -1 : 1 }).limit(count);

    res.json({
      candidates: data.map((doc) => ({
        id: doc._id,
        createdBy: doc.createdBy,
        isAnonymous: doc.isAnonymous,
        language: doc.language,
        createdAt: doc.createdAt,
        text: doc.text,
        status: doc.status,
        reviewedBy: doc.reviewedBy,
        reviewedAt: doc.reviewedAt,
        // uncomment this to add containsBanned
        // containsBanned: doc.containsBanned
      })),
    });
  }));

  router.post('/questionCandidates/status', checkPermissions(['approveQod', 'translator', 'translatorManager']), asyncHandler(async (req, res, next) => {
    const { status } = req.body;
    const { id } = req.body;

    if (!id || typeof id !== 'string' || typeof status !== 'string' || !['approved', 'rejected'].includes(status)) {
      throw invalidInputError();
    }

    // updates only those candidates with pending status
    const candidate = await QuestionCandidate.findOne({ _id: id, status: 'pending' });
    if (!candidate) {
      throw notFoundError();
    }

    if (req.user.adminPermissions.translator && req.user.adminPermissions.translator !== candidate.language) {
      throw forbiddenError();
    }

    candidate.status = status;
    candidate.reviewedBy = req.user._id;
    candidate.reviewedAt = new Date();
    await candidate.save();

    if (status === 'approved') {
      const question = await createQod({
        text: candidate.text,
        createdBy: candidate.isAnonymous ? null : candidate.createdBy,
        language: candidate.language,
      });
      if (!question) {
        console.error(`error posting qod candidate: ${id}`);
      } else if (candidate.createdBy) {
        const user = await User.findById(candidate.createdBy);
        if (user) {
          await coinsLib.updateCoins(
            { user: candidate.createdBy },
            { $inc: { coins: 50, qodRewardsReceived: 50 } },
            'qod approved',
          );
          // send Chat Message
          const messageText = translate(
            'Your question "%s", has been scheduled for %s',
            user.locale,
            question.text,
            question.createdAt.toLocaleString(user.locale, { day: 'numeric', month: 'long', year: 'numeric' }),
          );

          const sendReply = user.versionAtLeast('1.13.64')
            ? sendAutomatedReply
            : sendSupportUserReply;

          await sendReply(user, messageText);
        } else {
          console.error(`qod candidate ${id} user expected but not found`);
        }
      }
    }
    res.json({});
  }));

  router.get('/database/profileCandidate', checkPermissions('approveDatabase'), asyncHandler(async (req, res, next) => {
    const docs = await ProfileCandidate.aggregate([
      {
        $lookup: {
          from: 'subcategories',
          localField: 'subcategories',
          foreignField: 'id',
          as: 'subcategories',
        },
      },
    ]);

    res.json({
      candidates: docs,
    });
  }));

  router.post('/database/profileCandidate/status', checkPermissions('approveDatabase'), asyncHandler(async (req, res, next) => {
    const candidate = await ProfileCandidate.findOne({ _id: req.body._id });
    if (!candidate) {
      throw notFoundError();
    }
    if (req.body.status == 'approved') {
      throw forbiddenError();
      if (candidate.newSubcategory) {
        const category = await Category.findOne({ slug: candidate.newSubcategory.categorySlug });
        const doc = new Subcategory();
        doc.name = candidate.newSubcategory.name;
        doc.category = category.id;
        doc.slug = createSlug(doc.name);
        await doc.save();
        candidate.subcategories.push(doc.id);
      }
      const doc = new Profile(candidate);
      doc._id = new mongoose.Types.ObjectId();
      doc.isNew = true;
      await doc.save();
      await onUserDbUpload(candidate.createdBy);
    }
    await candidate.deleteOne();
    res.json({});
  }));

  router.get('/database/profileImageCandidate', checkPermissions('approveDatabase'), asyncHandler(async (req, res, next) => {
    const docs = await ProfileImageCandidate.aggregate([
      {
        $lookup: {
          from: 'profiles',
          localField: 'profile',
          foreignField: '_id',
          as: 'profile',
        },
      },
      {
        $unwind: '$profile',
      },
    ]);
    for (let i = 0; i < docs.length; i++) {
      await databaseLib.populateProfile(docs[i].profile);
    }
    res.json({
      candidates: docs,
    });
  }));

  router.post('/database/profileImageCandidate/status', checkPermissions('approveDatabase'), asyncHandler(async (req, res, next) => {
    const candidate = await ProfileImageCandidate.findOne({ _id: req.body._id });
    if (!candidate) {
      throw notFoundError();
    }
    if (req.body.status == 'approved') {
      const doc = await Profile.findOne({ _id: candidate.profile });
      doc.image = candidate.image;
      doc.imageSource = candidate.imageSource;
      doc.createdBy = candidate.createdBy;
      await doc.save();
      await onUserDbUpload(candidate.createdBy);
    } else {
      await s3.deletePicture(candidate.image);
    }
    await candidate.deleteOne();
    res.json({});
  }));

  router.get('/translations', checkPermissions(['translator', 'translatorManager']), asyncHandler(async (req, res, next) => {
    const { lastSent } = req.query;
    const { status } = req.query;
    const { language } = req.query;
    const { order } = req.query;
    let count = Number(req.query.count || CANDIDATE_MAX_COUNT);
    const matchQuery = {
      status: 'pending',
    };

    if (status) {
      if (['pending', 'approved', 'rejected'].includes(status)) {
        matchQuery.status = status;
      } else {
        throw invalidInputError();
      }
    }

    if (language) {
      if (languageCodes.includes(language)) {
        matchQuery.language = language;
      } else {
        throw invalidInputError();
      }
    }
    if (req.user.adminPermissions.translator) {
      matchQuery.language = req.user.adminPermissions.translator;
    }

    if (Number.isNaN(count)) {
      throw invalidInputError();
    } else if (count) {
      if (Number.isInteger(count)) {
        if (count < 0 || count > TRANSLATION_MAX_COUNT) {
          count = TRANSLATION_MAX_COUNT;
        }
      } else {
        throw invalidInputError();
      }
    }

    let reverse = true;
    if (order) {
      if (['asc', 'desc'].includes(order)) {
        reverse = (order == 'desc');
      } else { throw invalidInputError(); }
    }

    if (lastSent) {
      if (ObjectId.isValid(lastSent)) {
        matchQuery._id = { [reverse ? '$lt' : '$gt']: lastSent };
      } else { throw invalidInputError(); }
    }

    const data = await Translation.find(matchQuery).sort({ _id: (reverse) ? -1 : 1 }).limit(count).lean();

    res.json({
      translations: data,
    });
  }));

  router.post('/translations/status', checkPermissions(['translator', 'translatorManager']), asyncHandler(async (req, res, next) => {
    const { status } = req.body;
    const { id } = req.body;

    if (!id || typeof id !== 'string' || typeof status !== 'string' || !['approved', 'rejected'].includes(status)) {
      throw invalidInputError();
    }

    // updates only those candidates with pending status
    const translation = await Translation.findOne({ _id: id, status: 'pending' });
    if (!translation) {
      throw notFoundError();
    }

    if (req.user.adminPermissions.translator) {
      if (translation.language != req.user.adminPermissions.translator) {
        return next(forbiddenError());
      }
    }

    translation.status = status;
    translation.reviewedBy = req.user._id;
    translation.reviewedAt = new Date();
    await translation.save();

    if (status === 'approved') {
      if (translation.createdBy) {
        const user = await User.findById(translation.createdBy);
        if (user) {
          await coinsLib.updateCoins(
            { user: translation.createdBy },
            { $inc: { coins: TRANSLATION_REWARD, translationRewardsReceived: TRANSLATION_REWARD } },
            'translation approved',
          );
          // send Chat Message
          await sendSupportUserReply(
            user,
            translate(
              'Your translation has been approved.',
              user.locale,
            ),
          );
        } else {
          console.error(`user translation-  ${id} user expected but not found`);
        }
      }
    }
    res.json({});
  }));

  router.patch('/userConfig', checkPermissions('setConfig'), asyncHandler(async (req, res, next) => {
    const user = await User.findById(req.body.user);
    if (!user) {
      return next(notFoundError());
    }
    try {
      user.config[req.body.configName] = req.body.configValue;
      await user.save();
    } catch (error) {
      return next(invalidInputError());
    }
    res.json({});
  }));

  router.patch('/userGender', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const { user, gender } = req.body;
    if (!user || !['male', 'female', 'non-binary'].includes(gender)) {
      return next(invalidInputError());
    }
    const updateUser = await User.findById(user);
    if (!updateUser) {
      return next(notFoundError());
    }
    try {
      updateUser.gender = gender;
      await updateUser.save();
    } catch (error) {
      return next(invalidInputError());
    }
    res.json({});
  }));

  router.get('/receipts', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const { transactionId } = req.query;
    if (!transactionId || typeof transactionId !== 'string') {
      return next(invalidInputError());
    }

    const receipts = await Promise.all([
      PurchaseReceipt.findOne({ transactionId }),
      CoinPurchaseReceipt.findOne({ transactionId }),
      SuperLikePurchaseReceipt.findOne({ transactionId }),
      NeuronPurchaseReceipt.findOne({ transactionId }),
      BoostPurchaseReceipt.findOne({ transactionId }),
    ]);

    const result = receipts.find((receipt) => receipt !== null);

    if (!result) {
      return next(notFoundError());
    }

    res.json({
      user: result.user,
      purchaseDate: result.purchaseDate,
      productId: result.productId,
      currency: result.currency,
      price: result.price,
    });
  }));

  router.get('/qods/byUser', checkPermissions(['approveQod', 'support']), asyncHandler(async (req, res, next) => {
    const { user } = req.query;

    if (!user || typeof user !== 'string') {
      return next(invalidInputError());
    }

    const qods = await Question.find({ createdBy: user, interestName: 'questions' }, '_id createdAt text language')
      .sort({ createdAt: -1 })
      .limit(CANDIDATE_MAX_COUNT)
      .lean();

    const questionCandidates = await QuestionCandidate.find({ createdBy: user, status: 'pending' }, '_id createdAt text language isAnonymous')
      .sort({ createdAt: -1 })
      .limit(CANDIDATE_MAX_COUNT)
      .lean();

    res.json({
      qods,
      questionCandidates,
    });
  }));

  router.delete('/qod', checkPermissions(['approveQod', 'support']), asyncHandler(async (req, res, next) => {
    const { questionId } = req.query;

    if (!questionId || !ObjectId.isValid(questionId)) {
      return next(invalidInputError());
    }

    const qod = await Question.findById(questionId);
    if (!qod || qod.interestName !== 'questions') {
      return next(notFoundError());
    }

    const { createdAt, language } = qod;
    if (moment(createdAt).isBefore(moment())) {
      // if qod already went live, then change to anonymous instead of deleting
      qod.createdBy = null;
      await qod.save();
      return res.json({});
    }

    // Find the newest QOD for the same language and interestName
    const newestQod = await Question.findOne({ language, interestName: 'questions' })
      .sort({ createdAt: -1 })
      .lean();

    // Delete the current QOD
    await Question.deleteOne({ _id: questionId });
    if (newestQod && String(newestQod._id) !== String(qod._id)) {
      await Question.updateOne({ _id: newestQod._id }, { $set: { createdAt } });
    }
    res.json({});
  }));

  router.delete('/questionCandidate', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const { questionId } = req.query;
    if (!questionId || !ObjectId.isValid(questionId)) {
      return next(invalidInputError());
    }

    const deleted = await QuestionCandidate.deleteOne({ _id: questionId, status: 'pending' });
    if (deleted.deletedCount === 0) {
      return next(notFoundError());
    }
    res.json({});
  }));

  router.get('/scammerCleanup/review', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    let queue = req.query.queue;
    if (typeof queue == 'string') {
      queue = parseInt(queue);
    }
    if (!(typeof queue == 'number' && queue >= 1 && queue <= 7)) {
      return next(invalidInputError());
    }

    const profiles = await ScammerCleanup.find({
      $or: [
        { 'review1.queue': queue, 'review1.decision': null },
        { 'review2.queue': queue, 'review2.decision': null },
      ],
    }, '_id user verificationPhoto').limit(1000)

    res.json({ profiles });
  }));

  router.put('/scammerCleanup/review/decision', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const _id = req.body._id;
    let userId = req.body.user;
    const decision = req.body.decision;
    let queue = req.body.queue;
    if (typeof queue == 'string') {
      queue = parseInt(queue);
    }
    if (!(typeof queue == 'number' && queue >= 1 && queue <= 7)) {
      return next(invalidInputError());
    }
    if (!['ban', 'dismiss', 'verify', 'unverify'].includes(decision)) {
      return next(invalidInputError());
    }

    let doc;
    if (_id) {
      doc = await ScammerCleanup.findById(_id);
    } else if (userId) {
      doc = await ScammerCleanup.findOne({ user: userId }).sort('-createdAt');
    }
    if (!doc) {
      return next(invalidInputError());
    }
    userId = doc.user;

    let thisReview, otherReview;
    if (doc.review1.queue == queue) {
      thisReview = doc.review1;
      otherReview = doc.review2;
    } else if (doc.review2.queue == queue) {
      thisReview = doc.review2;
      otherReview = doc.review1;
    } else {
      return next(invalidInputError());
    }

    if (thisReview.decision) {
      return next(invalidInputError());
    }
    if (otherReview.reviewedBy && otherReview.reviewedBy == req.uid) {
      return next(invalidInputError());
    }

    thisReview.reviewedBy = req.uid;
    thisReview.reviewedAt = Date.now();
    thisReview.decision = decision;

    const user = await User.findById(userId);

    if (!otherReview.decision) {
      if (thisReview.decision == 'verify') {
        await handleVerificationDecision(user, true, undefined, req.uid);
      } else if (thisReview.decision == 'unverify') {
        await handleVerificationDecision(user, false, 'Photo unclear', req.uid);
      }
    } else {
      if (thisReview.decision == otherReview.decision) {
        doc.finalDecision = thisReview.decision;
        if (thisReview.decision == 'ban') {
          await shadowBan(user, req.uid, 'scammer verification photo cleanup', `also reviewed by ${otherReview.reviewedBy}`);
        }
      } else {
        doc.reviewFinal.required = true;
      }
    }

    await doc.save();

    res.json({});
  }));

  router.get('/scammerCleanup/reviewFinal', checkPermissions('manager'), asyncHandler(async (req, res, next) => {

    const profiles = await ScammerCleanup.find({
      'reviewFinal.required': true,
      'reviewFinal.decision': null,
    }, '_id user verificationPhoto review1 review2').limit(1000)

    res.json({ profiles });
  }));

  router.put('/scammerCleanup/reviewFinal/decision', checkPermissions('manager'), asyncHandler(async (req, res, next) => {
    const _id = req.body._id;
    let userId = req.body.user;
    const decision = req.body.decision;
    if (!['ban', 'dismiss', 'verify', 'unverify'].includes(decision)) {
      return next(invalidInputError());
    }

    let doc;
    if (_id) {
      doc = await ScammerCleanup.findById(_id);
    } else if (userId) {
      doc = await ScammerCleanup.findOne({ user: userId }).sort('-createdAt');
    }
    if (!doc) {
      return next(invalidInputError());
    }
    if (!doc.reviewFinal.required || doc.reviewFinal.decision) {
      return next(invalidInputError());
    }
    userId = doc.user;

    doc.reviewFinal.reviewedBy = req.uid;
    doc.reviewFinal.reviewedAt = Date.now();
    doc.reviewFinal.decision = decision;
    doc.finalDecision = decision;
    await doc.save();

    if (doc.finalDecision == 'ban') {
      const user = await User.findById(userId);
      if (user) {
        await shadowBan(user, req.uid, 'scammer verification photo cleanup');
      }
    }
    else if (doc.finalDecision == 'verify') {
      const user = await User.findById(userId);
      await handleVerificationDecision(user, true, undefined, req.uid);
    }
    else if (doc.finalDecision == 'unverify') {
      const user = await User.findById(userId);
      await handleVerificationDecision(user, false, 'Photo unclear', req.uid);
    }

    res.json({});
  }));

  router.get('/verifyProfileNew/review', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    let queue = req.query.queue;
    if (typeof queue == 'string') {
      queue = parseInt(queue);
    }
    if (!(typeof queue == 'number' && queue >= 1 && queue <= 2)) {
      return next(invalidInputError());
    }

    const users = await User.find({
      $or: [
        { 'verification.manualReview.review1.queue': queue, 'verification.manualReview.review1.decision': { $type: 'null' } },
        { 'verification.manualReview.review2.queue': queue, 'verification.manualReview.review2.decision': { $type: 'null' } },
      ],
    }, projections.userProfileAdminFields.join(' '))
      .sort('verification.updatedAt')
      .limit(100)
      .lean()
    users.forEach(user => normalizeUserFields(user));

    res.json({ users });
  }));

  router.put('/verifyProfileNew/review/decision', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    let userId = req.body.user;
    const decision = req.body.decision;
    let queue = req.body.queue;
    const rejectionReason = req.body.rejectionReason;
    const bannedReason = req.body.bannedReason;
    const bannedNotes = req.body.bannedNotes;
    if (typeof queue == 'string') {
      queue = parseInt(queue);
    }
    if (!(typeof queue == 'number' && queue >= 1 && queue <= 2)) {
      return next(invalidInputError('Invalid queue number'));
    }
    if (!['verify', 'reject', 'ban'].includes(decision)) {
      return next(invalidInputError('Invalid decision'));
    }

    const user = await User.findById(userId);
    if (!user) {
      return next(invalidInputError('User not found'));
    }

    let thisReview, otherReview;
    if (user.verification.manualReview.review1.queue == queue) {
      thisReview = user.verification.manualReview.review1;
      otherReview = user.verification.manualReview.review2;
    } else if (user.verification.manualReview.review2.queue == queue) {
      thisReview = user.verification.manualReview.review2;
      otherReview = user.verification.manualReview.review1;
    } else {
      return next(invalidInputError('Queue not found'));
    }

    if (thisReview.decision) {
      return next(invalidInputError('Already reviewed'));
    }
    if (otherReview.reviewedBy && otherReview.reviewedBy == req.uid) {
      return next(invalidInputError('Second decision must come from another team member'));
    }

    thisReview.reviewedBy = req.uid;
    thisReview.reviewedAt = Date.now();
    thisReview.decision = decision;
    if (rejectionReason) thisReview.rejectionReason = rejectionReason;
    if (bannedReason) thisReview.bannedReason = bannedReason;
    if (bannedNotes) thisReview.bannedNotes = bannedNotes;
    if (otherReview.decision) {
      if (thisReview.decision == otherReview.decision) {
        user.verification.manualReview.finalDecision = thisReview.decision;
      } else {
        user.verification.manualReview.reviewFinal.decision = null;
      }
    } else {
      user.verification.manualReview.firstDecision = decision;
    }
    user.verificationHistory.push({
      date: new Date(),
      manualReview: user.verification.manualReview,
    });
    await user.save();

    if (!otherReview.decision) {
      if (thisReview.decision == 'verify') {
        await handleVerificationDecision(user, true, undefined, req.uid, 'first decision in double queue system');
      } else if (thisReview.decision == 'reject') {
        await handleVerificationDecision(user, false, rejectionReason, req.uid, 'first decision in double queue system');
      } else if (thisReview.decision == 'ban') {
        await shadowBan(user, req.uid, bannedReason, `${bannedNotes} (banned during photo verification first review)`, {}, req.body.bannedReasons);
      }
    }

    // increment decision count
    await incrementDecision(req.uid, req.user.email, 1);

    res.json({});
  }));

  router.get('/verifyProfileNew/reviewFinal', checkPermissions(['manager','dualQueueTiebreaker']), asyncHandler(async (req, res, next) => {

    const users = await User.find({
      'verification.manualReview.reviewFinal.decision': { $type: 'null' },
    }, projections.userProfileAdminFields.join(' '))
      .populate('verification.manualReview.review1.reviewedBy', 'email')
      .populate('verification.manualReview.review2.reviewedBy', 'email')
      .sort('verification.updatedAt')
      .limit(100)
      .lean()
    users.forEach(user => normalizeUserFields(user));

    res.json({ users });
  }));

  router.put('/verifyProfileNew/reviewFinal/decision', checkPermissions(['manager','dualQueueTiebreaker']), asyncHandler(async (req, res, next) => {
    const _id = req.body._id;
    let userId = req.body.user;
    const decision = req.body.decision;
    const rejectionReason = req.body.rejectionReason;
    const bannedReason = req.body.bannedReason;
    const bannedNotes = req.body.bannedNotes;
    if (!['verify', 'reject', 'ban'].includes(decision)) {
      return next(invalidInputError('Invalid decision'));
    }

    const user = await User.findById(userId);
    if (!user) {
      return next(invalidInputError('User not found'));
    }

    if (user.verification.manualReview.reviewFinal.decision !== null) {
      return next(invalidInputError('Final decision already made'));
    }

    user.verification.manualReview.reviewFinal.reviewedBy = req.uid;
    user.verification.manualReview.reviewFinal.reviewedAt = Date.now();
    user.verification.manualReview.reviewFinal.decision = decision;
    user.verification.manualReview.finalDecision = decision;
    if (rejectionReason) user.verification.manualReview.reviewFinal.rejectionReason = rejectionReason;
    if (bannedReason) user.verification.manualReview.reviewFinal.bannedReason = bannedReason;
    if (bannedNotes) user.verification.manualReview.reviewFinal.bannedNotes = bannedNotes;
    user.verificationHistory.push({
      date: new Date(),
      manualReview: user.verification.manualReview,
    });
    await user.save();

    if (decision != user.verification.manualReview.firstDecision) {
      if (decision == 'ban') {
        await handleVerificationDecision(user, false, rejectionReason, req.uid, 'final decision in double queue system');
        await shadowBan(user, req.uid, bannedReason, `${bannedNotes} (banned during photo verification final review)`, {}, req.body.bannedReasons);
      }
      else if (decision == 'verify') {
        if (user.bannedReason?.includes('banned during photo verification first review') || user.bannedNotes?.includes('banned during photo verification first review')) {
          await reportLib.unban(user, req.uid, 'unbanned during photo verification final review');
          unBanAllUsersOnSameDevice(user, req.uid, 'unbanned during photo verification final review');
        }
        await handleVerificationDecision(user, true, undefined, req.uid, 'final decision in double queue system');
      }
      else if (decision == 'reject') {
        if (user.bannedReason?.includes('banned during photo verification first review') || user.bannedNotes?.includes('banned during photo verification first review')) {
          await reportLib.unban(user, req.uid, 'unbanned during photo verification final review');
          unBanAllUsersOnSameDevice(user, req.uid, 'unbanned during photo verification final review');
        }
        await handleVerificationDecision(user, false, rejectionReason, req.uid, 'final decision in double queue system');
      }
    }

    for (const review of [user.verification.manualReview?.review1, user.verification.manualReview?.review2]) {
      if (review?.decision !== undefined && review.decision !== decision) {
        // increment incorrect decision count for the reviewer
        await incrementDecision(review.reviewedBy, null, 0, 1);
      }
    }

    res.json({});
  }));

  router.get('/verifyProfileNew/leaderboard', checkPermissions('support'), asyncHandler(async (req, res, next) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const sevenDaysAgo = new Date(today);
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const leaderboard = await SupportAgentAccuracy.aggregate([
      {
        $match: { date: { $gte: sevenDaysAgo } },
      },
      {
        $group: {
          _id: '$supportAgentId',
          supportAgentEmail: { $first: '$supportAgentEmail' },
          totalDecisions: { $sum: '$totalDecisions' },
          incorrectDecisions: { $sum: '$incorrectDecisions' },
        },
      },
      {
        $addFields: {
          accuracyRate: {
            $cond: {
              if: { $gt: ['$totalDecisions', 0] },
              then: {
                $round: [
                  {
                    $multiply: [
                      {
                        $divide: [{ $subtract: ['$totalDecisions', '$incorrectDecisions'] }, '$totalDecisions'],
                      },
                      100,
                    ],
                  },
                  2,
                ],
              },
              else: 0,
            },
          },
        },
      },
      {
        $project: {
          supportAgentId: '$_id',
          supportAgentEmail: 1,
          totalDecisions: 1,
          incorrectDecisions: 1,
          accuracyRate: 1,
        },
      },
      {
        $sort: {
          accuracyRate: -1,
          totalDecisions: -1,
        },
      },
    ]);

    res.json({ leaderboard });
  }));

  return router;
};
