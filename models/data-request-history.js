const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const schema = new mongoose.Schema({
  userId: { type: String, ref: 'User' },
  requestedAt: { type: Date, default: Date.now },
  status: {
    type: String,
    enum: ['pending', 'completed', 'failed'],
    default: 'pending',
  },
  downloadKey: { type: String },
  emailedAt: { type: Date },
  retryCount: { type: Number, default: 0 },
  error: { type: String, default: undefined },
  processingTimeSec: { type: Number },
});

schema.index({ userId: 1, requestedAt: -1 });
schema.index({ status: 1 });

schema.statics.getLastRequest = async function (userId) {
  return this.findOne({ userId }).sort({ requestedAt: -1 });
};

// Export schema =======================================================================
let conn = connectionLib.getRecordsConnection() || mongoose;
module.exports = conn.model('DataRequestHistory', schema);
