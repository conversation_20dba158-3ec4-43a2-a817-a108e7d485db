const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const schema = new mongoose.Schema({
  chatId: { type: String, ref: 'Chat' },
  requestedBy: { type: String, ref: 'User' },
  requestedAt: { type: Date, default: Date.now },
  status: {
    type: String,
    enum: ['pending', 'approved', 'completed', 'failed'],
    default: 'pending',
  },
  downloadKey: { type: String },
  emailedAt: { type: Date },
  retryCount: { type: Number, default: 0 },
  error: { type: String, default: undefined },
  processingTimeSec: { type: Number },
});

schema.index({ chatId: 1, requestedAt: -1 });
schema.index({ status: 1 });

// Export schema =======================================================================
let conn = connectionLib.getRecordsConnection() || mongoose;
module.exports = conn.model('ChatExportHistory', schema);
